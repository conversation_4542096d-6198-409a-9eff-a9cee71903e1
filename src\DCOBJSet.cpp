#include "DCOBJSet.h"
#include <pthread.h>

static pthread_mutex_t o_mutex = PTHREAD_MUTEX_INITIALIZER;
DCOBJPool* DCOBJPool::m_inst = NULL;

DCOBJPool* DCOBJPool::instance()
{
	if(!m_inst)
	{
		m_inst = new DCOBJPool();
	}
	return m_inst;
}

DCOBJPool::DCOBJPool()
	:m_max(DCOBJPOOL_DEFAULT_SIZE)
	,m_cad(0)
{

}

int DCOBJPool::reg(const DCBOBJ& obj)
{
	int ret = -1;
	pthread_mutex_lock(&o_mutex);
    if(m_regd.count(obj.type()) == 0)
	{
		m_regd[obj.type()] = obj.clone();
		ret = 0;
	}
	pthread_mutex_unlock(&o_mutex);
	return ret;
}

DCBOBJ* DCOBJPool::get(const char* type)
{
	DCBOBJ* pobj = NULL;
	pthread_mutex_lock(&o_mutex);
	std::vector<DCBOBJ*>& vobj = m_cache[type];
    if(vobj.empty())
	{
		size_t rsize = m_used.size() + m_cad;
		if(!m_regd.empty() && rsize < m_max)
		{
            for(std::map<std::string, DCBOBJ*>::iterator it = m_regd.begin(); it != m_regd.end(); it++)
			{
				pobj = it->second->clone();
			}
		}
	}
	else
	{
		pobj = vobj.back();
		vobj.pop_back();
		m_cad--;
	}
	if(pobj)m_used.insert(pobj);
	pthread_mutex_unlock(&o_mutex);
	return pobj;
}

void DCOBJPool::put(DCBOBJ* pobj)
{
	if(!pobj) return;
	pthread_mutex_lock(&o_mutex);
	std::set<DCBOBJ*>::iterator it = m_used.find(pobj);
    if(it == m_used.end())
	{
        delete pobj;
	}
	else
	{
		m_used.erase(it);
		size_t rsize = m_used.size() + m_cad;
		if(rsize < m_max)
		{
			m_cache[pobj->type()].push_back(pobj);
			pobj->clear();
			m_cad++;
		}
		else
		{
			delete pobj;
		}
	}
	pthread_mutex_unlock(&o_mutex);
}

void DCOBJPool::adjust(size_t max_size)
{
	m_max = max_size;
}

size_t  DCOBJPool::regd_size()
{
	return m_regd.size();
}

size_t  DCOBJPool::used_size()
{
	return m_used.size();
}

size_t  DCOBJPool::cached_size()
{
	return m_cad;
}


/*--------------------------------------------------------------------*/
static pthread_mutex_t s_mutex = PTHREAD_MUTEX_INITIALIZER;
DCOBJSetPool* DCOBJSetPool::m_inst = NULL;

DCOBJSetPool* DCOBJSetPool::instance()
{
    if(!m_inst)
	{
		m_inst = new DCOBJSetPool();
	}
	return m_inst;
}

DCOBJSetPool::DCOBJSetPool()
	:m_max(DCOBJSETPOOL_DEFAULT_SIZE)
{

}

DCOBJSetPool::~DCOBJSetPool()
{
}

int DCOBJSetPool::reg(const DCBOBJ& obj)
{
	int ret = -1;
	pthread_mutex_lock(&s_mutex);
    if(m_regd.count(obj.type()) == 0)
	{
		m_regd[obj.type()] = obj.clone();
		ret = 0;
	}
	pthread_mutex_unlock(&s_mutex);
	return ret;
}

DCOBJSet* DCOBJSetPool::get()
{
	DCOBJSet* pset = NULL;
	pthread_mutex_lock(&s_mutex);
    if(m_cache.empty())
	{
		size_t rsize = m_used.size() + m_cache.size();
		if(!m_regd.empty() && rsize < m_max)
		{
			pset = new DCOBJSet();
            for(std::map<std::string, DCBOBJ*>::iterator it = m_regd.begin(); it != m_regd.end(); it++)
			{
				pset->m_set.push_back(it->second->clone());
			}
		}
	}
	else
	{
		pset = m_cache.back();
		m_cache.pop_back();
	}
	if(pset)m_used.insert(pset);
	pthread_mutex_unlock(&s_mutex);
	return pset;
}

void DCOBJSetPool::put(DCOBJSet* pset)
{
    if(!pset) return;
	pthread_mutex_lock(&s_mutex);
	std::set<DCOBJSet*>::iterator it = m_used.find(pset);
    if(it == m_used.end())
	{
        delete pset;
	}
	else
	{
		m_used.erase(it);
		size_t rsize = m_used.size() + m_cache.size();
		if(rsize < m_max)
		{
			m_cache.push_back(pset);
			pset->clear();
		}
		else
		{
			delete pset;
		}
	}
	pthread_mutex_unlock(&s_mutex);
}

void DCOBJSetPool::adjust(size_t max_size)
{
	m_max = max_size;
}

size_t DCOBJSetPool::regd_size()
{
	return m_regd.size();
}

size_t  DCOBJSetPool::used_size()
{
	return m_used.size();
}

size_t  DCOBJSetPool::cached_size()
{
	return m_cache.size();
}
