<?xml version="1.0" encoding="ansi"?>
<doc>
<dcflow name="RBRFlOW" flow_plugin="FLOW_RBR">
	<flow name="FLOW_MSGREQ" callrel="CALL_MSGREQ">
		<result>
			<rc value="OK"    flow="FLOW_QRYUSERINFO"/>
			<rc value="FAIL"  flow="FLOW_MSGANS"/>
		</result>
	</flow>
	<flow name="FLOW_QRYUSERINFO" callrel="CALL_QRYUSERINFO">
		<result>
			<rc value="OK"    flow="FLOW_QRYOFRINFO"/>
			<rc value="FAIL"  flow="FLOW_MSGANS"/>
		</result>
	</flow>
	<flow name="FLOW_QRYOFRINFO" callrel="CALL_QRYOFRINFO">
		<result>
			<rc value="OK"    flow="FLOW_QRYRATABLEINFO"/>
			<rc value="FAIL"  flow="FLOW_MSGANS"/>
		</result>
	</flow>
	<flow name="FLOW_QRYRATABLEINFO" callrel="FL_QRYRATABLEINFO">
		<result>
			<rc value="OK"    flow="FLOW_RATING"/>
			<rc value="FAIL"  flow="FLOW_MSGANS"/>
		</result>
	</flow>
	<flow name="FLOW_RATING" callrel="FL_RATING">
		<result>
			<rc value="OK"    flow="FLOW_MSGANS"/>
			<rc value="FAIL"  flow="FLOW_MSGANS"/>
		</result>
	</flow>
	<flow name="FLOW_MSGANS" callrel="FL_MSGANS">
	</flow>
</dcflow>
</doc>