TINY_BASE=$(TINY_HOME)
TINY_INC=$(TINY_BASE)/include
TINY_SRC=$(TINY_BASE)/src
TINY_OBJ=$(TINY_BASE)/obj
TINY_LIB=$(TINY_BASE)/lib

INCLUDE=-I$(TINY_INC)

OBJECTS=$(TINY_OBJ)/tinystr.o \
		$(TINY_OBJ)/tinyxml.o \
		$(TINY_OBJ)/tinyxmlerror.o \
		$(TINY_OBJ)/tinyxmlparser.o \
		$(TINY_OBJ)/DCParseXml.o

LIB=$(TINY_LIB)/libtinyxml.a

all:$(LIB)
	
$(LIB):$(OBJECTS)
	$(AR) $(LIB) $(OBJECTS)
	
$(OBJECTS):$(TINY_OBJ)/%.o:$(TINY_SRC)/%.cpp
	$(CC) -c $(CFLAGS) $< -o $@ $(INCLUDE)
	
clean:
	@rm -rf $(OBJECTS) $(LIB)
