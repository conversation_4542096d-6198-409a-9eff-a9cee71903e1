#include "DCMCastManer.h"
#include <sys/types.h>
#include <sys/unistd.h>
#include <sys/socket.h>
#include <sys/poll.h>
#include <string.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <pthread.h>
#include <errno.h>
#include <stdlib.h>
#include <map>
#include <string>

///------------------------------helper function--------------------------------------------///
static inline const char* strnchr(const char* src, int ch, int size)
{
	for(int i = 0; i<size; i++)
	{
        if(src[i]==ch) return src+i;
	}
	return NULL;
}

///------------------------------DCMCastGroup--------------------------------------------///
DCMCastGroup::DCMCastGroup(const char*ip, int port)
	:m_send_fd(-1)
	,m_recv_fd(-1)
{
    memset(&m_addr, 0, sizeof(m_addr));
	m_addr.sin_family = AF_INET;
	m_addr.sin_addr.s_addr = inet_addr(ip);
	m_addr.sin_port = htons((unsigned short)port);
}

DCMCastGroup::DCMCastGroup(const char*addr)
	:m_send_fd(-1)
	,m_recv_fd(-1)
{
	int port = 0;
	char sIp[80]={0};
	const char* sep = strchr(addr, ':');
	if(sep)
	{
		strncpy(sIp, addr, sep-addr);
		port = atoi(sep+1);
	}
	else
	{
		strncpy(sIp, addr, sizeof(sIp)-1);
	}

	memset(&m_addr, 0, sizeof(m_addr));
	m_addr.sin_family = AF_INET;
	m_addr.sin_addr.s_addr = inet_addr(sIp);
	m_addr.sin_port = htons((unsigned short)port);
}

DCMCastGroup::~DCMCastGroup()
{
    if(m_send_fd != -1)
	{
        close(m_send_fd);
	}
	if(m_recv_fd != -1)
	{
        unbind();
	}
}

int DCMCastGroup::bind()
{
	int ret = 0;
	int on = 0;
	int errnum = 0;
    if(m_recv_fd != -1)
	{
        return 0;
	}

	// allocate recv socket
    m_recv_fd = socket(AF_INET, SOCK_DGRAM, 0);
    if(m_recv_fd == -1)
	{
        return -1;
	}

	// reused ip address
	on = 1;
	ret = setsockopt(m_recv_fd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(on));
	if(ret < 0)
	{
		errnum = errno;
		close(m_recv_fd);
		m_recv_fd = -1;
		errno = errnum;
		return -1;
	}

    // bind local address
    struct sockaddr_in local_addr;
	memset(&local_addr, 0, sizeof(local_addr));
	local_addr.sin_family = AF_INET;
    local_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    local_addr.sin_port = m_addr.sin_port;

	ret = ::bind(m_recv_fd, (struct sockaddr*)&local_addr, sizeof(local_addr));
	if(ret < 0)
	{
		errnum = errno;
		close(m_recv_fd);
		m_recv_fd = -1;
		errno = errnum;
		return -1;
	}

    // add mulitcast group
	struct ip_mreq mreq;
    mreq.imr_multiaddr.s_addr = m_addr.sin_addr.s_addr;
    mreq.imr_interface.s_addr = htonl(INADDR_ANY);

    ret = setsockopt(m_recv_fd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq));
    if(ret < 0)
	{
		errnum = errno;
		close(m_recv_fd);
		m_recv_fd = -1;
		errno = errnum;
		return -1;
	}
	return 0;
}

int DCMCastGroup::unbind()
{
	// leave mulitcast group
	struct ip_mreq mreq;
    mreq.imr_multiaddr.s_addr = m_addr.sin_addr.s_addr;
    mreq.imr_interface.s_addr = htonl(INADDR_ANY);

	setsockopt(m_recv_fd, IPPROTO_IP, IP_DROP_MEMBERSHIP, &mreq, sizeof(mreq));
	close(m_recv_fd);
	m_recv_fd = -1;
	return 0;
}

int DCMCastGroup::send(const char*msg, int len)
{
	int ret = 0;
    if(m_send_fd == -1)
	{
		// allocate send socket
        m_send_fd = socket(AF_INET, SOCK_DGRAM, 0);
        if(m_send_fd == -1)
		{
			return -1;
		}

		// allow multicast send
		int loop = 1;
		ret = setsockopt(m_send_fd, IPPROTO_IP, IP_MULTICAST_LOOP, &loop, sizeof(loop));
		if(ret < 0)
		{
            close(m_send_fd);
            m_send_fd = -1;
            return -1;
		}
	}

    // send msg to multicast address
	ret = sendto(m_send_fd, (void*)msg, len, MSG_NOSIGNAL, (struct sockaddr*)&m_addr, sizeof(m_addr));
	return ret;
}

int DCMCastGroup::recv(char*msg, int len, int timeout_ms)
{
	int ret = 0;
    struct pollfd fd[1];
    fd[0].fd = m_recv_fd;
    fd[0].events = POLLIN;
    fd[0].revents = 0;

    if(m_recv_fd == -1)
	{
		return -1;
	}

	ret = poll(fd, 1, timeout_ms);
	if(ret != 1 || !(fd[0].revents&POLLIN) )
	{
		return -1;
	}

    ret = recvfrom(m_recv_fd, (void*)msg, len, 0, NULL, NULL);
	return ret;
}

///------------------------------DCMCastManerImp--------------------------------------------///
class DCMCastManerImp
{
public:
	DCMCastManerImp()
		:m_tid(0)
		,m_mcast(NULL)
	{
		pthread_mutex_init(&m_mutex, NULL);
	}

	~DCMCastManerImp()
	{
		if(m_tid)
		{
			pthread_cancel(m_tid);
		}
              pthread_mutex_destroy(&m_mutex);
		delete m_mcast;

              for(std::map<std::string, DCMCastManer::EventFun*>::iterator it = m_mfun.begin(); 
                   it != m_mfun.end(); it++)
              {
                    delete it->second;
              }
	}

    int init(const char* addr)
    {
		int ret = 0;
		m_mcast = new DCMCastGroup(addr);

		ret = m_mcast->bind();
		if(ret < 0)
		{
            return ret;
		}

		ret = pthread_create(&m_tid, NULL, work_routine, (void*)this);
		if(ret < 0)
		{
            return ret;
		}
		return 0;
    }

	void bind_event(const char* name, DCMCastManer::EventFun* fun)
	{
		pthread_mutex_lock(&m_mutex);
              if(m_mfun.count(name))
              {
                    delete m_mfun[name];
              }
		m_mfun[name] = fun;
		pthread_mutex_unlock(&m_mutex);
	}

    void unbind_event(const char* name)
    {
        pthread_mutex_lock(&m_mutex);
            if(m_mfun.count(name))
               {
                    delete m_mfun[name];
                    m_mfun.erase(name);
               }
		pthread_mutex_unlock(&m_mutex);
    }

    void call_event(const char* name, const char* value)
    {
    	pthread_mutex_lock(&m_mutex);
		std::map<std::string, DCMCastManer::EventFun*>::iterator it = m_mfun.find(name);
    	if(it != m_mfun.end())
		{
    	    it->second->callback(name, value);
		}
		else  //熔断消息解析
		{
		    if(strstr(name, "CirB"))
			{
			    it = m_mfun.find("CirB");
		    	if(it != m_mfun.end())
				{
		    	    it->second->callback(name, value);
				}
			}
			else if(strstr(name, "CirBR"))
			{
			    it = m_mfun.find("CirBR");
		    	if(it != m_mfun.end())
				{
		    	    it->second->callback(name, value);
				}
			}
		}
				
		pthread_mutex_unlock(&m_mutex);
    }

	DCMCastGroup* mcast()
	{
		return m_mcast;
	}

private:

	static void* work_routine(void* handle)
	{
		DCMCastManerImp* imp = static_cast<DCMCastManerImp*>(handle);
		imp->svc();
		return NULL;
	}

	int svc()
	{
        while(1)
		{
            run_loop();
		}
		return 0;
	}

	int run_loop(int timeout_ms = -1);

private:
    pthread_t						       m_tid;
    pthread_mutex_t					m_mutex;
	DCMCastGroup*					m_mcast;
    std::map<std::string, DCMCastManer::EventFun*> m_mfun;
};

int DCMCastManerImp::run_loop(int timeout_ms)
{
	int ret = 0;
	int pos = 0;
    bool brun = true;
	const char* sep = NULL;
	const char* tail = NULL;
    char buf[1024*4]={0};

    while(brun)
	{
		ret = m_mcast->recv(buf+pos, sizeof(buf)-pos-1, timeout_ms);
    	if(ret <= 0)
		{
    	    return ret;
		}
		buf[pos+ret]=0x0;

		if(ret < sizeof(buf)-pos)
		{
			brun = false;	// 消息已接收完整
		}
		else
		{
			timeout_ms = 0;	// 后续接收不能等待
		}

    	while(pos < ret)
		{
			tail = strchr(buf+pos, '\n');
			if(!tail)
			{
                memmove(buf, buf+pos, ret-pos);
                pos = ret-pos;
                break;
			}
            sep = strnchr(buf+pos, '=', tail-buf-pos);
            if(sep)
			{
				*(char*)sep = 0x0;
            	*(char*)tail = 0x0;

            	call_event(buf+pos, sep+1);
			}
			pos = tail-buf+1;
		}
	}
	return 0;
}

///------------------------------DCMCastManer--------------------------------------------///

struct EventFunWrap__ : public DCMCastManer::EventFun
{
	EV_MGFUN m_fun;

	EventFunWrap__(EV_MGFUN fun)
		:m_fun(fun)
	{}

	virtual void callback(const char*n,const char*v)
	{
        m_fun(n,v);
	}
};

DCMCastManer::DCMCastManer()
{
	m_imp = new DCMCastManerImp();
}

DCMCastManer::~DCMCastManer()
{
	delete m_imp;
}

int DCMCastManer::init(const char* addr)
{
	return m_imp->init(addr);
}

void DCMCastManer::bind_event(const char* name, EV_MGFUN fun)
{
	m_imp->bind_event(name, new EventFunWrap__(fun));
}

void DCMCastManer::bind_event(const char* name, EventFun* fun)
{
	m_imp->bind_event(name, fun);
}

void DCMCastManer::unbind_event(const char* name)
{
	m_imp->unbind_event(name);
}

DCMCastGroup* DCMCastManer::mcast()
{
	return m_imp->mcast();
}
