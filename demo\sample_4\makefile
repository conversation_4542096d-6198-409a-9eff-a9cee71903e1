include ../../comm.mk



PWD=$(shell pwd)

COMMON_INC=$(LBSPUBROOT)/SM/common/include
INCLUDE =-I$(AVRO)/include -I$(DCLOGCLI)/include -I$(COMMON_INC) -I$(ITF)/include -I$(DFM_INC_PATH) -I$(PWD)/include  -I$(TXML)/include -I$(MQ)/include
INCLUDEALL =$(INCLUDE) -I$(DCLOGCLI)/include -I$(JSTORM_INC) -I$(JSON_INC)  -I$(ZK_INC) -I$(DCA_INC)/json_dca -I$(ACE_INC_PATH)

LIBPATH=-L$(AVRO)/lib -L$(RELEASE_PATH)/lib -L$(DCLOGCLI)/lib -L$(DFM_LIB_PATH) -L$(JSTORM_LIB) -L$(JSON_LIB) -L$(ZK_LIB) -L$(DCA_LIB) -L$(MQ)/lib $(TXML)/lib/libtinyxml.a -L$(ACE_LIB_PATH)
LIBSLIST=  -ldclogcli  

LIBSALL=-ldclogcli -ldfm -lcstorm -lzookeeper_mt  -ldcmq -lACE

sample=$(FM)/demo/sample_4

inc=-I$(FM)/include

version=1.0.0


bintarget=$(PWD)/sample
.PHONY:all clean

all: $(bintarget)
	
$(bintarget):$(PWD)/sam_sql.o
	$(CC) $(LFLAGS) -o $@ $^ $(LIBPATH)  $(LIBSALL) $(LIBSLIST)
	
$(PWD)/%.o:%.cpp
	$(CC) $(CFLAGS) -c $< $(INCLUDE)	$(INCLUDEALL)

	
clean:
	-rm -f  $(bintarget) *.o
	
