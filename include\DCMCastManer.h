#ifndef __DCMCASTMANER_H__
#define __DCMCASTMANER_H__
#include <netinet/in.h>

/***--multicast group recv and send--***/
/***--reserved   ip range: ********* - *********** --***/
/***--known      ip range: ********* - *********** --***/
/***--user used  ip range: ********* - *************** --***/
/***--local used ip range: ********* - *************** --***/

class DCMCastGroup
{
public:
	DCMCastGroup(const char*ip, int port);

	DCMCastGroup(const char*address);

	~DCMCastGroup();

	int bind();

	int unbind();

	int send(const char*msg, int len);

    int recv(char*msg, int len, int timeout_ms = -1);

private:
	int		m_send_fd;
	int		m_recv_fd;
	struct sockaddr_in	m_addr;
};

/***--recv multicast msg and callback event_func --***/
typedef void(*EV_MGFUN)(const char*name, const char*value);

class DCMCastManerImp;
class DCMCastManer
{
public:
    struct EventFun
    {
        virtual void callback(const char*,const char*) = 0;
    };
public:
	DCMCastManer();

	~DCMCastManer();

    int init(const char* addr);

	void bind_event(const char* name, EV_MGFUN fun);

	void bind_event(const char* name, EventFun* fun);

    void unbind_event(const char* name);

	DCMCastGroup* mcast();

private:
	DCMCastManerImp* m_imp;
};

#endif // __DCMCASTMANER_H__
