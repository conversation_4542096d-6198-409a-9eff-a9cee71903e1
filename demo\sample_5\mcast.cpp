#include "DCMCastGroup.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>

bool g_run = true;

const char* mode = NULL;
const char* ip = NULL;
int port = 0;
int sleep_us = 1000000;

void exit_signal(int sig)
{
    if(sig == SIGINT || sig == SIGTERM || sig==SIGQUIT)
	{
		g_run = false;
	}
}

void* send_thread(void*arg)
{
	DCMCastGroup mgp(ip, port);
	long pid = getpid();
	unsigned int i = 0;
	int length = 0;
	int ret = 0;
	char buf[32*1024]={0};

	while(g_run)
	{
		i++;
		length = sprintf(buf, "multicast send: num[%u] pid[%ld] ip[%s] port[%d]", i, pid, ip, port);
		ret = mgp.send(buf, length);
		if(ret < 0)
		{
			fprintf(stderr, "send [%u] failed: %s\n", i, strerror(errno));
		}
		else
		{
			fprintf(stdout, "send [%u] success\n", i);
		}
		usleep(sleep_us);
	}
	return NULL;
}

void* recv_thread(void*arg)
{
	DCMCastGroup mgp(ip, port);
	long pid = getpid();
	unsigned int i = 0;
	int length = 0;
	int ret = 0;
	char buf[32*1024]={0};

	ret = mgp.bind();
    if(ret < 0)
	{
		fprintf(stderr, "bind failed\n");
		return NULL;
	}
	fprintf(stdout, "bind success\n");
	while(g_run)
	{
		i++;
		ret = mgp.recv(buf, sizeof(buf));
        if(ret < 0)
		{
			fprintf(stderr, "recv [%u] failed: %s\n", i, strerror(errno));
		}
        else
		{
			fprintf(stdout, "recv [%u] success:[%s]\n",i, buf);
		}
		usleep(sleep_us);
	}
	return NULL;
}

int main(int argc, char* argv[])
{
	if(argc < 4)
	{
        printf("Usage: %s -m [send|recv] -i muticast_ip -p port -s usleep\n", argv[0]);
        return 1;
	}
	int opt = 0;
    while((opt = getopt(argc, argv, "m:i:p:s:")) != -1)
	{
		switch (opt) {
		case 'm':
			mode = optarg;
			break;
		case 'i':
			ip = optarg;
			break;
		case 'p':
			port = atoi(optarg);
			break;
		case 's':
			sleep_us = atoi(optarg);
			if(sleep_us < 0)
			{
				sleep_us = 0;
			}
			break;
		}
	}

    if(!mode || !ip || !port)
	{
		printf("Usage: %s -m [send|recv] -i muticast_ip -p port -s usleep\n", argv[0]);
        return 1;
	}

	if(strcmp(mode,"send") && strcmp(mode,"recv"))
	{
		printf("Usage: %s -m [send|recv] -i muticast_ip -p port -s usleep\n", argv[0]);
        return 1;
	}

	signal(SIGINT, exit_signal);
	signal(SIGTERM, exit_signal);
	signal(SIGQUIT, exit_signal);

    if(!strcmp(mode,"send"))
	{
		send_thread(NULL);
	}
	else
	{
        pthread_t tid[2];
        for(int i = 0; i< 2; i++)
		{
			pthread_create(&tid[i], NULL, recv_thread, NULL);
		}

        for(int i =0; i< 2; i++)
		{
            pthread_join(tid[i], NULL);
		}
	}
	fprintf(stdout, "process exit\n");
	return 0;
}

