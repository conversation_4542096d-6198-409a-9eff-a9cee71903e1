# author: lidong
# use for parallel compile
# usually we can just set FILES and TLIB,that's enough.
# if we have multiply targets,we can use (FILE1,TLIB1,OBJ1) for one target,
# (FILE2,TLIB2,OBJ2) for another target.
# but we should make SRCS contain all .cpp files of all FILES,
# and make PCCSRCS contain all .cpp files from all .pc files of all FILES,
# and make OBJS contain all .o files from .cpp or .pc of all FILES,
# and make DEPD contain all .d files from SRCS and PCCSRCS.
include ../../comm.mk

PWD=$(shell pwd)

ifeq ("$(RELEASE_PATH)/","/")
RELEASE_PATH=$(PWD)
endif

ifeq ("$(PROJECT_RPATH)/","/")
PROJECT_RPATH=$(PWD)
endif

# set source directory
SRCPATH=$(PWD)/src
BUILDPATH=$(PWD)/$(BUILD_PATH)

# we should write .cpp or .pc files to here
FILES=DCBasePlugin.cpp DCBaseFlow.cpp DCPluginManer.cpp DCPerfStatistic.cpp  DCDBManer.cpp DCOBJSet.cpp DCRFData.cpp DCSeriaOp.cpp DCMCastManer.cpp DCMCastEvtFun.cpp DCMon.cpp 


# Macro definition
MACROS = -D DFM_DEBUG_

CIRBRE=$(THIRD_HOME)/CircuitBreaker

# set COMMON_INCS and COMMON_LIBS
COMMON_INCS:=-I$(PWD)/include -I$(DCLOGCLI)/include -I$(TXML)/include  -I$(AVRO)/include  -I$(JSONC)/include -I$(ENCRYPT) -I$(CIRBRE)/include
COMMON_LIBS:=-L$(TXML)/lib -L$(AVRO)/lib -L$(JSONC)/lib -L$(ENCRYPT) -L$(DCLOGCLI)/lib -L$(CIRBRE)/lib

# set static library name
TLIB= $(BUILDPATH)/libdfm_s.a

# set dynamic library name
DLIB=$(BUILDPATH)/libdfm.so
DLinkLib=  -ltinyxml -ldl -lpthread -lavrocpp_s -ljson_s -lencrypt -ldclogcli -lcircuitbreaker

# automatic obtain .cpp and .o files
# obtain .cpp files from FILES
SRCS=$(call GetCC, $(SRCPATH), $(FILES))
# obtain .o files from FILES
OBJS=$(call GetObj, $(BUILDPATH), $(FILES))
# obtain .cpp files from .pc files
PCCSRCS=$(call GetPCC, $(SRCPATH), $(FILES))
# obtain .d from .cpp files
DEPD=$(subst .cpp,.d, $(SRCS) $(PCCSRCS))

# create build directory
tmpvar:=$(call CreateDir, $(BUILDPATH))
#tmpvar=$(call CreateDir, $(RELEASE_PATH)/bin)
tmpvar:=$(call CreateDir, $(RELEASE_PATH)/lib)


.PHONY: all clean distclean dup
all: $(DLIB) $(TLIB)

# we should use copy more than move
$(TLIB): $(OBJS)
	$(AR) $@ $?
	ranlib $@
	@cp -f $(TLIB) $(RELEASE_PATH)/lib && \
	echo "copy $(TLIB) to $(RELEASE_PATH)/lib"

$(DLIB): $(OBJS)
	$(CC) $(LDFLAGS) $(DLLFLAGS) -o $@ $^ $(COMMON_LIBS) $(DLinkLib)
	@cp -f $(DLIB) $(RELEASE_PATH)/lib && \
	echo "copy $(DLIB) to $(RELEASE_PATH)/lib"
	
clean:
	@- rm -f $(TLIB) $(DLIB) $(OBJS) $(PCCSRCS)

distclean:clean
	@- rm -rf $(DEPD) $(BUILDPATH)
	
dup:
	@cp -pf $(DLIB) $(PROJECT_RPATH)/lib && \
	echo "dup $(DLIB) to $(PROJECT_RPATH)/lib"
	
	
# include .d files for trace .h files
ifeq "$(DEPFLAG)" "0"
include $(DEPD)
endif
	
# useful rules ######################################################################
# we should supply OBJS, PCCSRCS, BUILDPATH, SRCPATH and COMMON_INCS  to use them.
# for compile .cpp
$(OBJS):$(BUILDPATH)/%.o:$(SRCPATH)/%.cpp
	cd $(SRCPATH) && \
	$(CC) $(CXXFLAGS) $(MACROS) $(COMMON_INCS) -c $(<F) -o $@
	
# for compile .pc
$(PCCSRCS):$(SRCPATH)/%.cpp:$(SRCPATH)/%.pc	
	cd $(SRCPATH) && \
	$(PCC) $(PCCFLAGS) iname=$(<F) oname=$@

# for name.o -> /path/name.o
%.o:$(BUILDPATH)/%.o

# useful rules ######################################################################
