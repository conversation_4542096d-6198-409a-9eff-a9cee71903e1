# 数据库管理器（DCDBManer）

## 项目简介
DCDBManer 是一个C++实现的数据库管理工具，提供了数据库连接管理、SQL语句管理、性能统计等功能。主要特点包括：

1. 支持主备数据库切换
2. 动态SQL语句管理
3. 性能统计
4. 分表分库支持

## 核心功能

### 1. 数据库连接管理
- 支持主备数据库模式
- 自动重连机制
- 连接状态检查

### 2. SQL语句管理
- 动态SQL语句添加/删除
- SQL语句预处理（Prepare）
- 支持按模块管理SQL

### 3. 数据库模式
支持以下四种数据库连接模式：
- DBME_MASTER: 主连接优先
- DBME_SLAVE: 备连接优先
- DBMN_MASTER: 仅主连接
- DBMN_SLAVE: 仅备连接

## 主要接口说明

### 初始化
```cpp
int Init(const char* sqlf, bool prepared = true);
```
- sqlf: SQL配置文件路径（app.sql.xml）
- prepared: 是否预处理SQL语句

### 设置数据库模式
```cpp
void SetDBMode(DCDBMODE mode);
```
- mode: 数据库连接模式

### SQL操作
```cpp
// 添加或修改SQL
int SetSQL(const char* mod, const char* name, const char* sub, const char* param, const char* sqltext);

// 获取SQL句柄
UDBSQL* GetSQL(const char* name);

// 删除SQL
void DelSQL(const char* name);
```

### 连接管理
```cpp
// 检查并重连
int CheckReset();

// 快速检查重连
int FastReset();
```

## 使用示例
请参考 `test/test_dcdbmaner.cpp` 文件中的测试用例，该文件展示了DCDBManer类的主要接口使用方法。

## 配置文件
系统需要一个XML格式的SQL配置文件（app.sql.xml），用于存储SQL语句和相关配置信息。

## 注意事项
1. 使用前需要正确配置数据库连接信息
2. 建议在程序启动时进行初始化
3. 需要正确处理数据库连接异常
4. 在使用SQL语句前确保已经进行了预处理

## 性能优化建议
1. 适当使用SQL预处理功能
2. 合理设置主备数据库模式
3. 定期检查数据库连接状态
4. 使用性能统计功能监控系统性能 