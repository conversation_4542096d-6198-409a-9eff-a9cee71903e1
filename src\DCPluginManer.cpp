#include "DCPluginManer.h"
#include "DCPerfStatistic.h"
#include "DCDBManer.h"
#include "DCRFData.h"
#include "DCMCastManer.h"
#include "DCLogMacro.h"

#include <string>
#include <map>
#include <vector>
#include <tinyxml.h>
#include <dlfcn.h>
#include <pthread.h>
#include <errno.h>

typedef DCBasePlugin* (*PLUGIN_FUNC)();

/*---------------------------------------DCPluginManerImp---------------------------------------------*/
class DCPluginManerImp
{
public:
	DCPluginManerImp(DCPluginManer* base)
		:m_base(base)
	{
		m_mapStat["APP"] = new DCPerfStatistic("APP");
		m_dbm = NULL;
		m_mcm = new DCMCastManer();
	}

    ~DCPluginManerImp()
    {
        clear();
        delete m_mcm;
    }

    int init(const char* path, const char* app_name, int umode = 0);

    int load_flow(const char* name)
    {
		if(get_flow(name) != NULL)
		{
            return 0;
		}
		if(create_flow_plugin(name) != NULL)
		{
			return 0;
		}
        return -1;
    }

	DCBaseFlow* get_flow(const char* name)
	{
		std::map<std::string, DCBaseFlow*>::iterator itflow = m_mFlow.find(name);
    	if(itflow == m_mFlow.end())
		{
    	    return NULL;
		}
		return itflow->second;
	}

	DCPerfStatistic* get_statistic(const char* szInstanceName)
	{
		if (m_mapStat.find(std::string(szInstanceName)) == m_mapStat.end())
		{
			m_mapStat[std::string(szInstanceName)] = new DCPerfStatistic( std::string(szInstanceName) );
		}

		return m_mapStat[std::string(szInstanceName)];
	}

	DCDBManer* get_dbm()
	{
		pthread_t tid = pthread_self();
		if(tid == m_tid)
		{
	        return m_dbm;
		}
	    return NULL;
	}

	DCRFData* get_drf()
	{
		return DCRFData::instance();
	}

	DCMCastManer* get_mcm()
	{
		return m_mcm;
	}

	void set_handle(const char* name, void* handle)
	{
		if(name && name[0] && handle)
		{
            m_hdl[name] = handle;
		}
	}

    void* get_handle(const char* name)
    {
        std::map<std::string, void*>::iterator it = m_hdl.find(name);
        if(it != m_hdl.end())
		{
			return it->second;
		}
		return NULL;
    }

	void clear();

private:
    struct STPluginInfo
    {
        char name[64];
        char caty[64];
        char version[12];
        void* dlhandle;
		PLUGIN_FUNC create;
        int  ncount;
    };

    struct STCallRel
    {
        char name[64];
        char plugin[64];
		std::vector<STCallRel*> vcall;
    };

    struct STFlowItemDesc
    {
        char name[64];
		char callrel[64];
		std::vector<std::pair<std::string, std::string> > next;
    };

private:
	int create_sql_info(const char*sfile);

    int create_plugin_info(const char* sfile);

    int create_call_rel(const char* sfile);

    void create_call(STCallRel& caller, TiXmlElement* parent);

    int create_flow_info(const char* sfile);

    int create_one_flow(TiXmlElement* flow_root);

    DCBaseFlow* create_flow_plugin(const char* flow_name);

	DCBasePlugin* create_call_rel_plugin(const char* name, DCBasePlugin* bflow, const char* flow_name);

    int create_call_plugin(STCallRel& caller, DCBasePlugin* parent, DCBasePlugin* bflow, const char* flow_name);

    void clear_call(STCallRel& caller);

    DCBasePlugin* create_plugin(STPluginInfo* info, DCBasePlugin* bflow, const char* flow_name);

	STPluginInfo* get_plugin(const char* name);

	STCallRel* get_callrel(const char* name);

private:
	DCPluginManer*				m_base;
	std::map<std::string, DCPerfStatistic*> m_mapStat;
    std::vector<STPluginInfo>	m_vPluginInfo;
    std::vector<STCallRel>		m_vCallRel;
    std::map<std::string, std::vector<STFlowItemDesc> > m_mFlowDesc;
    std::map<std::string, DCBaseFlow*> m_mFlow;
    std::map<std::string, void*>	m_hdl;
    pthread_t					m_tid;
	DCDBManer*					m_dbm;
	DCMCastManer*               m_mcm;
};

int DCPluginManerImp::init(const char* path, const char* app_name, int umode)
{
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "plugin init, path[%s], app_name[%s], umode[%d]", path, app_name, umode);

	int ret = 0;
	char app[64]={0};
	char latn[16]={0};
	char buf[512];
	clear();
	
	const char* p = strchr(app_name, '|');
	if(p)
	{
		strncpy(app, app_name, p-app_name);
		strcpy(latn, p+1);
	}
	else
	{
		strcpy(app,app_name);
	}

	m_tid = pthread_self();

    const char* s_mcast = getenv("OCS_MCAST_CMD_ADDR");
    if(s_mcast)
	{
		ret = m_mcm->init(s_mcast);
		if(ret < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "init DCMCastManer failed, addr[%s], err[%s]", s_mcast, strerror(errno));
			return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "init DCMCastManer success for addr[%s]", s_mcast);
	}
	else
	{
		DCSYSLOG(DCLOG_LEVEL_WARN, 0, "not find the env[OCS_MCAST_CMD_ADDR]");
	}
	set_handle("MCAST", (void*)m_mcm);

    if(DFM_USE_DBM&umode)
	{
		sprintf(buf, "%s/%s%s.sql.xml", path, app, latn);
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start create_sql_info, path[%s]", buf);
		ret = create_sql_info(buf);
		if(ret < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "create_sql_info failed, path[%s]", buf);
    	    return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create_sql_info success");

		if(DFM_USE_REFRESH&umode)
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start init DCRFData sql path[%s]", buf);
            ret = DCRFData::instance()->init(buf);
            if(ret < 0)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "init DCRFData failed, sql path[%s]", buf);
    	    	return ret;
			}
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "init DCRFData success");
		}
	}

	sprintf(buf, "%s/%s.plugin.xml", path, app);
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start create_plugin_info, path[%s]", buf);
	ret = create_plugin_info(buf);
	if(ret < 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "create_plugin_info failed, path[%s]", buf);
		return ret;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create_plugin_info success");

	sprintf(buf, "%s/%s.callrel.xml", path, app);
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start create_call_rel, path[%s]", buf);
    ret = create_call_rel(buf);
    if(ret < 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "create_call_rel failed, path[%s]", buf);
		return ret;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create_call_rel success");

	sprintf(buf, "%s/%s.flow.xml", path, app);
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start create_flow_info, path[%s]", buf);
    ret = create_flow_info(buf);
	if(ret < 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "create_flow_info failed, path[%s]", buf);
		return ret;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create_flow_info success");

	if(!(DFM_USE_NOFLOW&umode))
	{
        for(std::map<std::string, std::vector<STFlowItemDesc> >::iterator it = m_mFlowDesc.begin();
        	it != m_mFlowDesc.end(); it++)
		{
			DCBaseFlow* flow = create_flow_plugin(it->first.c_str());
			if(!flow)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, ret, "create_flow_plugin failed, name[%s]", it->first.c_str());
				return -1;
			}
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create_flow_plugin success, name[%s]", it->first.c_str());
		}
	}else{
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "don't create flow plugin now");
	}
	return 0;
}

void DCPluginManerImp::clear()
{
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "destroy all plugins");
	for(std::map<std::string, DCBaseFlow*>::iterator it = m_mFlow.begin(); it != m_mFlow.end(); it++)
	{
		delete it->second;
	}
	m_mFlow.clear();
	m_mFlowDesc.clear();

	for(std::vector<STCallRel>::iterator itc = m_vCallRel.begin(); itc != m_vCallRel.end(); itc++)
	{
		clear_call(*itc);
	}
	m_vCallRel.clear();

    for(std::vector<STPluginInfo>::iterator itp = m_vPluginInfo.begin(); itp != m_vPluginInfo.end(); itp++)
	{
        if(itp->dlhandle)
		{
			dlerror();
			dlclose(itp->dlhandle);
		}
	}
	m_vPluginInfo.clear();

	for (std::map<std::string, DCPerfStatistic *>::iterator itr = m_mapStat.begin(); itr != m_mapStat.end(); itr++)
	{
		itr->second->clear();
	}

	delete m_dbm;
	m_dbm = NULL;
}

void DCPluginManerImp::clear_call(STCallRel& caller)
{
	for(std::vector<STCallRel*>::iterator it = caller.vcall.begin(); it != caller.vcall.end(); it++)
	{
		clear_call(*(*it));
        delete *it;
	}
	caller.vcall.clear();
}

DCPluginManerImp::STPluginInfo* DCPluginManerImp::get_plugin(const char* name)
{
	std::vector<STPluginInfo>::iterator iter;
    for(iter = m_vPluginInfo.begin(); iter != m_vPluginInfo.end(); iter++)
	{
        if(strcmp(iter->name, name) == 0)
		{
            return &(*iter);
		}
	}
	return NULL;
}

DCPluginManerImp::STCallRel* DCPluginManerImp::get_callrel(const char* name)
{
	std::vector<STCallRel>::iterator iter;
    for(iter = m_vCallRel.begin(); iter != m_vCallRel.end(); iter++)
	{
        if(strcmp(iter->name, name) == 0)
		{
            return &(*iter);
		}
	}
	return NULL;
}

int DCPluginManerImp::create_plugin_info(const char* sfile)
{
    TiXmlDocument doc(sfile);

    doc.LoadFile();
    if(doc.Error())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load file[%s] failed: %s", sfile, doc.ErrorDesc());
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load file[%s] success", sfile);
	TiXmlElement* root_elem = doc.RootElement();
	if(!root_elem)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find root element for file[%s]", sfile);
		return -1;
	}

	TiXmlElement* plugin = NULL;
	const char* attr = NULL;
	STPluginInfo stpf;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load plugins");

	for(plugin = root_elem->FirstChildElement("plugin"); plugin; plugin = plugin->NextSiblingElement("plugin"))
	{
		memset(&stpf, 0, sizeof(stpf));
		attr = plugin->Attribute("name");
		if(!attr)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] for plugin[%lu]", m_vPluginInfo.size());
			return -1;
		}
		strcpy(stpf.name, attr);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find plugin name[%s]", stpf.name);

		attr = plugin->Attribute("category");
		if(!attr)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[category] for plugin[%s]", stpf.name);
			return -1;
		}
		strcpy(stpf.caty, attr);

		attr = plugin->Attribute("version");
		if(!attr)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[version] for plugin[%s]", stpf.name);
			return -1;
		}
		strcpy(stpf.version, attr);

		attr = plugin->GetText();
		if(!attr || !attr[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid plugin path for plugin[%s]", stpf.name);
            return -1;
		}

		std::string spath = attr;
		int pos = spath.rfind('/');
		if(std::string::npos==pos)//没有找到
		{
			char *szenv=getenv("OCS_HOME");
			char buf[256]={0};
			strncpy(buf,szenv,sizeof(buf)-1);
			strcat(buf,"/plugin/");
			strcat(buf,attr);

			DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "plugin name[%s], category[%s], version[%s], path[%s]", \
					stpf.name, stpf.caty, stpf.version, buf);

			dlerror();
			stpf.dlhandle = dlopen(buf, RTLD_LAZY);
			if(!stpf.dlhandle)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "dlopen plugin path[%s] failed:%s", buf, dlerror());
				return -1;
			}
		}
		else
		{
			DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "plugin name[%s], category[%s], version[%s], path[%s]", \
					stpf.name, stpf.caty, stpf.version, attr);

			dlerror();
			stpf.dlhandle = dlopen(attr, RTLD_LAZY);
			if(!stpf.dlhandle)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "dlopen plugin path[%s] failed:%s", attr, dlerror());
				return -1;
			}

		}


		stpf.create = (PLUGIN_FUNC)dlsym(stpf.dlhandle, "s_plugin_create");
        if(!stpf.create)
		{
            dlclose(stpf.dlhandle);
            DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find symbol[%s]", "s_plugin_create");
            return -1;
		}

		const char* category = *(const char**)dlsym(stpf.dlhandle, "mod_category");
		const char* func = *(const char**)dlsym(stpf.dlhandle, "mod_func");
		const char* version = *(const char**)dlsym(stpf.dlhandle, "mod_version");

		if(!category || !func || !version)
		{
			dlclose(stpf.dlhandle);
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the symbol [mod_category] [mod_func] [mod_version] must always exist");
            return -1;
		}

		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "plugin symbol func[%s], category[%s], version[%s], dlhandle[%p]", \
				func, category, version, stpf.dlhandle);

		if(strcmp(stpf.name, func))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the plugin name[%s] != mod_func[%s]", stpf.name, func);
                    dlclose(stpf.dlhandle);
                    return -1;
		}

		if(strcmp(stpf.caty, category))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the plugin category[%s] != mod_category[%s]", stpf.caty, category);
                     dlclose(stpf.dlhandle);
                     return -1;
		}

		if(strcmp(stpf.version, version))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the plugin version[%s] != mod_version[%s]", stpf.version, version);
                     dlclose(stpf.dlhandle);
                     return -1;
		}
		m_vPluginInfo.push_back(stpf);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "plugin[%s] check ok!", stpf.name);
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load plugins, size[%lu]", m_vPluginInfo.size());
	return 0;
}

int DCPluginManerImp::create_call_rel(const char* sfile)
{
	TiXmlDocument doc(sfile);

    doc.LoadFile();
    if(doc.Error())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load file[%s] failed: %s", sfile, doc.ErrorDesc());
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load file[%s] success", sfile);
	TiXmlElement* root_elem = doc.RootElement();
	if(!root_elem)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find root element for file[%s]", sfile);
		return -1;
	}

	TiXmlElement* callrel = NULL;
	const char* attr = NULL;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load callrels");

	for(callrel = root_elem->FirstChildElement("callrel"); callrel; callrel=callrel->NextSiblingElement("callrel"))
	{
		m_vCallRel.push_back(STCallRel());
		STCallRel& caller = m_vCallRel.back();

		caller.name[0] = 0;
		caller.plugin[0]=0;

		attr = callrel->Attribute("name");
		if(!attr)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] for callrel[%lu]", m_vCallRel.size()-1);
			return -1;
		}
		strcpy(caller.name, attr);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find callrel name[%s]", caller.name);

		attr = callrel->Attribute("start_plugin");
		if(!attr)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[start_plugin] for callrel[%s]", caller.name);
			return -1;
		}
		strcpy(caller.plugin, attr);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find callrel start_plugin[%s]", caller.plugin);

        create_call(caller, callrel);
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load callrels, size[%lu]", m_vCallRel.size());
	return 0;
}

void DCPluginManerImp::create_call(STCallRel& caller, TiXmlElement* parent)
{
	TiXmlElement* child = NULL;
	const char* attr = NULL;
	STCallRel* stcall = NULL;
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load call for parent[%s]", caller.name);
    for(child = parent->FirstChildElement("call"); child; child = child->NextSiblingElement("call"))
	{
		attr = child->Attribute("plugin");
        if(!attr || !attr[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "not find Attribute[plugin], ignore this");
            continue;
		}

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find call plugin [%s]", attr);

		stcall = new STCallRel();
		stcall->name[0] = 0x0;
		strcpy(stcall->plugin, attr);

		caller.vcall.push_back(stcall);

		create_call(*stcall, child);
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load call for parent[%s], size[%lu]", caller.name, caller.vcall.size());
}

int DCPluginManerImp::create_flow_info(const char* sfile)
{
	TiXmlDocument doc(sfile);

	doc.LoadFile();
    if(doc.Error())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load file[%s] failed: %s", sfile, doc.ErrorDesc());
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load file[%s] success", sfile);
	TiXmlElement* root_elem = doc.RootElement();
	if(!root_elem)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find root element for file[%s]", sfile);
		return -1;
	}

	TiXmlElement* flow_root = NULL;
	const char* flow_name = NULL;
	const char* flow_plugin=NULL;
	int ret = 0;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load dcflow");

	for(flow_root=root_elem->FirstChildElement("dcflow"); flow_root; flow_root=flow_root->NextSiblingElement("dcflow"))
	{
		flow_name = flow_root->Attribute("name");
        if(!flow_name || !flow_name[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] for dcflow[%lu], ignore this", m_mFlow.size());
			continue;
		}

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find dcflow name[%s]", flow_name);

		if(m_mFlowDesc.count(flow_name))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the dcflow[%s] is already exist, skip this", flow_name);
            continue;
		}

		flow_plugin = flow_root->Attribute("flow_plugin");
        if(!flow_name || !flow_name[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[flow_plugin] for dcflow[%s]", flow_name);
			return -1;
		}

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find dcflow flow_plugin[%s]", flow_plugin);

		ret = create_one_flow(flow_root);
		if(ret < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create plugin object failed for dcflow[%s]", flow_name);
			return ret;
		}
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load dcflow, size[%lu]", m_mFlowDesc.size());
	return 0;
}

int DCPluginManerImp::create_one_flow(TiXmlElement* flow_root)
{
	TiXmlElement* iflow = NULL;
	TiXmlElement* result = NULL;
	const char* flow_name = NULL;
	const char* flow_plugin=NULL;
	std::vector<STFlowItemDesc> vFlowDesc;
	const char* name = NULL;
	const char* crel = NULL;

	flow_name = flow_root->Attribute("name");
	flow_plugin = flow_root->Attribute("flow_plugin");

    //流程插件放在第0个位置
	vFlowDesc.push_back(STFlowItemDesc());
	STFlowItemDesc& dcflow = vFlowDesc.back();
	strcpy(dcflow.name, flow_name);
	strcpy(dcflow.callrel, flow_plugin);

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load sub flow for dcflow[%s]", flow_name);

	for(iflow = flow_root->FirstChildElement("flow"); iflow; iflow = iflow->NextSiblingElement("flow"))
	{
        name = iflow->Attribute("name");
        if(!name || !name[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] for flow[%lu]", vFlowDesc.size()-1);
            return -1;
		}

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find flow name[%s]", name);

		crel = iflow->Attribute("callrel");
		if(!crel || !crel[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[callrel] for flow[%s]", name);
			return -1;
		}

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find flow callrel[%s]", crel);

		vFlowDesc.push_back(STFlowItemDesc());
		STFlowItemDesc& siflow = vFlowDesc.back();

		strcpy(siflow.name, name);
		strcpy(siflow.callrel, crel);
		siflow.next.clear();

		result=iflow->FirstChildElement("result");
		if(result)
		{
			for(result = result->FirstChildElement("rc"); result; result=result->NextSiblingElement("rc"))
			{
                const char* value = result->Attribute("value");
                const char* inext = result->Attribute("flow");
                if(!value || !value[0])
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[value] in result/rc for flow[%s]", siflow.name);
					return -1;
				}
                if(!inext)
				{
					inext="";
				}
				siflow.next.push_back(make_pair(std::string(value), std::string(inext)));

				DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find flow result/rc value[%s], next flow[%s]", value, inext);
			}
		}
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load sub flow for dcflow[%s], size[%lu]", flow_name, vFlowDesc.size()-1);
	m_mFlowDesc[flow_name].swap(vFlowDesc);
	return 0;
}

DCBaseFlow* DCPluginManerImp::create_flow_plugin(const char* flow_name)
{
	if(m_mFlowDesc.count(flow_name) == 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find flow desc for %s", flow_name);
		return NULL;
	}

	std::vector<STFlowItemDesc>&vFlowDesc = m_mFlowDesc[flow_name];

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to create plugin object for dcflow[%s]", flow_name);

	STPluginInfo* info = get_plugin(vFlowDesc[0].callrel);
	if(!info)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find plugin info, name[%s]", vFlowDesc[0].callrel);
        return NULL;
	}

    DCBaseFlow* bflow = dynamic_cast<DCBaseFlow*>(info->create());
    if(!bflow)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create DCBaseFlow plugin[%s] failed", info->name);
        return NULL;
	}
	static_cast<DCBasePlugin*>(bflow)->m_pluginman = m_base;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create DCBaseFlow plugin[%s] success", info->name);

	std::string prefix = std::string(flow_name) + "." + std::string(info->name);
	static_cast<DCBasePlugin*>(bflow)->m_timepos = m_mapStat["APP"]->get_position(prefix.c_str());

	DCSYSLOG(DCLOG_LEVEL_INFO, 0, "DCBaseFlow plugin[%s] statistic name[%s]", info->name, prefix.c_str());

	if(bflow->initialize() < 0)
	{
		delete bflow;

		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "initialize DCBaseFlow plugin[%s] failed", info->name);
        return NULL;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "initialize DCBaseFlow plugin[%s] success", info->name);

    for(std::vector<STFlowItemDesc>::iterator it = vFlowDesc.begin()+1; it != vFlowDesc.end(); it++)
	{
		bflow->m_flows.push_back(DCBaseFlow::STFlowItem());
		DCBaseFlow::STFlowItem& sflow = bflow->m_flows.back();
		strcpy(sflow.name, it->name);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to create plugin object for flow[%s]", sflow.name);
		sflow.start = create_call_rel_plugin(it->callrel, bflow, flow_name);
		if(!sflow.start)
		{
			delete bflow;
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create_call_rel_plugin for flow[%s] failed", sflow.name);
            return NULL;
		}
	}

	for(unsigned int i = 1; i < vFlowDesc.size(); i++)
	{
		DCBaseFlow::STFlowItem& sflow = bflow->m_flows[i-1];

		for(unsigned int j=0; j < vFlowDesc[i].next.size(); j++)
		{
			sflow.next[vFlowDesc[i].next[j].first] = bflow->get_flowitem(vFlowDesc[i].next[j].second.c_str());
		}
	}

	m_mFlow[std::string(flow_name)] = bflow;
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to create plugin object for dcflow[%s]", flow_name);
    return  bflow;
}

DCBasePlugin* DCPluginManerImp::create_call_rel_plugin(const char* name, DCBasePlugin* bflow, const char* flow_name)
{
	STCallRel* caller = get_callrel(name);
	if(!caller)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find callrel[%s]", name);
		return NULL;
	}

	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "find callrel[%s], start plugin[%s], vcall size[%lu]", \
			caller->name, caller->plugin, caller->vcall.size());

	STPluginInfo* info = get_plugin(caller->plugin);
	if(!info)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find plugin info[%s]", caller->plugin);
        return NULL;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to find plugin info for start plugin[%s]", caller->plugin);

	DCBasePlugin* start = create_plugin(info, bflow, flow_name);
	if(!start)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create plugin[%s] failed", info->name);
		return NULL;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create plugin[%s] success", info->name);

	if(create_call_plugin(*caller, start, bflow, flow_name) < 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create child plugin for [%s] failed", info->name);
        return NULL;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create plugin object for callrel[%s] success", info->name);
    return start;
}

int DCPluginManerImp::create_call_plugin(STCallRel& caller, DCBasePlugin* parent, DCBasePlugin* bflow, const char* flow_name)
{
	std::vector<STCallRel*>::iterator iter;
	STPluginInfo* info = NULL;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to create child plugin for parent[%s]", caller.plugin);
	for(iter = caller.vcall.begin(); iter != caller.vcall.end(); iter++)
	{
		info = get_plugin((*iter)->plugin);
        if(!info)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find plugin info for child plugin[%s]", (*iter)->plugin);
            return -1;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to find plugin info for child plugin[%s]", info->name);

		DCBasePlugin* base = create_plugin(info, bflow, flow_name);
        if(!base)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create plugin[%s] failed", info->name);
            return -1;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create plugin[%s] success", info->name);

        if(create_call_plugin(*(*iter), base, bflow, flow_name) < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create child plugin for [%s] failed", info->name);
            return -1;
		}
		parent->m_calls.push_back(base);
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to create child plugin for parent[%s], size[%lu]", caller.plugin, parent->m_calls.size());
	return 0;
}

DCBasePlugin* DCPluginManerImp::create_plugin(STPluginInfo* info, DCBasePlugin* bflow, const char* flow_name)
{
	DCBasePlugin* base = bflow->get_child_plugin(info->name);
	if(base)
	{
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "DCBasePlugin plugin[%s] is already exist, reuse it", info->name);
		return base;
	}

	base = info->create();
    if(!base)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create DCBasePlugin plugin[%s] failed", info->name);
		return NULL;
	}
	base->m_pluginman = m_base;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create DCBasePlugin plugin[%s] success", info->name);

	std::string pre(flow_name);
	pre.append(".").append(info->name);
	base->m_timepos = m_mapStat["APP"]->get_position(pre.c_str());

	DCSYSLOG(DCLOG_LEVEL_INFO, 0, "DCBasePlugin plugin[%s] statistic name[%s]", info->name, pre.c_str());

	if(base->initialize() < 0)
	{
        delete base;
        DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "initialize DCBasePlugin plugin[%s] failed", info->name);
        return NULL;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "initialize DCBasePlugin plugin[%s] success", info->name);

	bflow->m_calls.push_back(base);
    return base;
}

int DCPluginManerImp::create_sql_info(const char*sfile)
{
    m_dbm = new DCDBManer();
    if(!m_dbm)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create DCDBManer failed");
		return -1;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to init DCDBManer for sql file [%s]", sfile);
    int ret = m_dbm->Init(sfile);
    if(ret < 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "init DCDBManer failed");
		return ret;
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to init DCDBManer");
	return 0;
}


/*---------------------------------------DCPluginManer---------------------------------------------*/
DCPluginManer::DCPluginManer()
{
	m_imp = new DCPluginManerImp(this);
}

DCPluginManer::~DCPluginManer()
{
	delete m_imp;
}

int DCPluginManer::init(const char* path, const char* app_name, int umode)
{
	return m_imp->init(path, app_name, umode);
}

int DCPluginManer::load_flow(const char* name)
{
	return m_imp->load_flow(name);
}

DCBaseFlow* DCPluginManer::get_flow(const char* name)
{
	return m_imp->get_flow(name);
}

DCPerfStatistic* DCPluginManer::get_statistic(const char* szInstanceName)
{
	return m_imp->get_statistic(szInstanceName);
}

DCDBManer* DCPluginManer::get_dbm()
{
    return m_imp->get_dbm();
}

DCRFData* DCPluginManer::get_drf()
{
	return m_imp->get_drf();
}

DCMCastManer* DCPluginManer::get_mcm()
{
	return m_imp->get_mcm();
}

void DCPluginManer::set_handle(const char* name, void* handle)
{
	return m_imp->set_handle(name, handle);
}

void* DCPluginManer::get_handle(const char* name)
{
	return m_imp->get_handle(name);
}

void DCPluginManer::clear()
{
	m_imp->clear();
}


