#ifndef __DCDBMANER_H__
#define __DCDBMANER_H__

#include <DCUDB.h>
#include <string>
#include <vector>
#include <map>


struct DCSQLDesc
{
	std::string name;       /*sql名字*/
    std::string db;			/*数据库实例名*/
	std::string mod;		/*模块名*/
	std::string policy;		/*模块加载策略：must 启动时prepare，demand 使用时prepare, none 不加载*/
	std::string sub;		/*sql子名列表，以逗号分隔*/
	std::string param;		/*sql绑定参数类型列表*/
	std::string sql;		/*sql语句*/
};

enum DCDBMODE
{
    DBME_MASTER		= 0,	/*主连接优先*/
	DBME_SLAVE,				/*备连接优先*/
	DBMN_MASTER,			/*仅主连接*/
	DBMN_SLAVE				/*仅备连接*/
};

enum CONN_TYPE
{
    CONN_TYPE_TT = 0,
    CONN_TYPE_DMDB = 1,
    CONN_TYPE_M2DB = 2,
    CONN_TYPE_ORACLE = 3,
    CONN_TYPE_DCA = 4,
    CONN_TYPE_HBASE = 5,
    CONN_TYPE_MYSQL=6,
    CONN_TYPE_PG=7
};


class DCPerfStatistic;
class DCDBManerIMP;
class DCDBManer
{
public:
	/* 构造函数 */
	DCDBManer();

	/* 析构函数 */
	~DCDBManer();

	/* 初始化函数，sqlf 为 app.sql.xml 文件路径 */
    int Init(const char* sqlf, bool prepared = true);

	/* 设置主备使用模式 */
    void SetDBMode(DCDBMODE mode);

    /* 检查并重连 */
    int CheckReset();

	/* 快速检查重连 */
    int FastReset();

	/* 按实例名获取数据库连接句柄 */
	UDBConnection* GetConnection(const char* dbname);

	/* 按模块名获取数据库连接句柄 */
	UDBConnection* GetModConnection(const char* mod);

	/* 按名获取SQL句柄 */
	UDBSQL* GetSQL(const char* name);

	/* 修改或新增SQL */
	int  SetSQL(const char* mod, const char* name, const char* sub, const char* param, const char* sqltext);

	/* 删除SQL句柄 */
	void DelSQL(const char* name);

	/* 按模块名Prepare SQL集合 */
	int  PreparedSQLByMod(const char* name);

	/* 按名获取SQL描述信息 */
	bool GetSQLDescByName(const char* name, DCSQLDesc& desc);

	/* 按模块名获取SQL描述信息集合 */
	bool GetSQLDescByMod(const char* name, std::vector<DCSQLDesc>& desc);

    /* 获取统计信息 */
	DCPerfStatistic* get_statistic();

	/* 分表分库迁移 */
	int DivRepair(const char* mod = "ALL");	
	
	int GetAllConnStr(std::multimap<int,std::string> &mulConnStr);
private:
	DCDBManerIMP* m_imp;
};

#endif // __DCDBMANER_H__
