#ifndef __DCUDB_H__
#define __DCUDB_H__

#include <exception>
#include <string>
#include <string.h>
#include <stdio.h>

#define DYN_DBCONN_CREATE(className, cate, ver) \
extern "C" { \
UDBConnection* s_dbconn_create(const char* name) \
{ \
    return new className(cate, name, ver); \
} \
const char* mod_category = cate ; \
const char* mod_version = ver ; \
}

enum UDBSTATE
{
    UDBS_NONE		= 0,	/* 无状态或未知状态 */
	UDBS_DB_LINK,			/* 数据库连接正常 */
	UDBS_DB_UNLINK,			/* 数据库连接异常，需重连 */
	UDBS_DB_ANY_LINK,		/* 数据库连接至少一个是正常的，可重连 */
    UDBS_STMT_VALID,		/* SQL句柄正常 */
    UDBS_STMT_INVALID		/* SQL句柄异常，需重置 */
};

enum UDBSTMT
{
	UDQLSTMT			= 1, /* DQL */
	UDMLSTMT			= 2	 /* DML */
};

class UDBSQL;
class UDBConnection
{
public:
	/* 构造函数 */
	UDBConnection(const char* category /* ="MDB" */, const char* name /* ="ocstt" */, const char* version /* ="1.0.0" */)
	{
        strcpy(m_category, category);
        strcpy(m_dbname, name);
        strcpy(m_version, version);
	}

	/* 析构函数 */
	virtual ~UDBConnection(){}

	/* 数据库实现名 */
	const char* category(){return m_category;}

	/* 数据库实例名 */
	const char* name(){return m_dbname;}

	/* 数据库实现版本号 */
	const char* version(){return m_version;}

	/* 连接数据库 */
	virtual int Connect(const char* str, const char* env = 0) = 0;

	/* 释放数据库 */
	virtual int Destroy() = 0;

	/* 重连数据库 */
	virtual int Reset(const char* str = 0) = 0;

	/* 事务提交 */
	virtual void Commit() = 0;

	/* 事务回滚 */
	virtual void Rollback() = 0;

	/* 获取连接状态 */
	virtual UDBSTATE State(bool force=false) = 0;

	/* 获取SQL句柄 */
	virtual UDBSQL* Stmt(const char* sql) = 0;

	/* 获取连接串 */
	virtual const char* GetConStr() = 0;

private:
	/* 禁止拷贝 */
	UDBConnection(const UDBConnection&);
	UDBConnection& operator=(const UDBConnection&);

private:
	char m_category[64];
	char m_dbname[64];
    char m_version[12];
};

class UDBSQL
{
public:
	/* 构造函数 */
	UDBSQL(){}

	/* 析构函数 */
	virtual ~UDBSQL(){}

	/* 获取SQL状态 */
	virtual UDBSTATE State() = 0;

	/* 获取SQL类型 */
	virtual UDBSTMT  StmtType() = 0;

	/* 获取连接句柄 */
	virtual UDBConnection* Connection() = 0;

	/* 重置SQL：重新prepare, 绑定参数类型 */
	virtual void Reset() = 0;

	/* 执行分表分库，需SQL操作之前执行, key=NULL 表示全表执行 */
    virtual void DivTable(const char* key) = 0;

	/* 执行SQL语句：DQL/DML */
	virtual void Execute() = 0;

	/* 获取查询下一行数据 */
	virtual bool Next() = 0;

	/* 查询结束关闭：M2DB */
	virtual void Close() = 0;

	/* 获取DML操作影响结果数 */
    virtual int  GetRowCount() = 0;

	/* 设置绑定参数类型：int */
	virtual void SetIntParam(int pos) = 0;

	/* 设置绑定参数类型：long */
	virtual void SetLongParam(int pos) = 0;

	/* 设置绑定参数类型：char */
	virtual void SetCharParam(int pos) = 0;

	/* 设置绑定参数类型：blob */
	virtual void SetBlobParam(int pos) = 0;

    /* 按参数列表设置绑定参数类型，'1' int, '2' long, '3' char, '4' blob */
	virtual void SetBindParam(const std::string& para) = 0;

	/* 获取绑定参数类型列表 */
	virtual void GetBindParam(std::string& para) = 0;

	/* 取消绑定参数值 */
	virtual void UnBindParam() = 0;

	/* 设置绑定参数：int */
	virtual void BindParam(int pos, int value) = 0;

	/* 设置绑定参数：long */
	virtual void BindParam(int pos, long value) = 0;

	/* 设置绑定参数：long */
	virtual void BindParam(int pos, long long value) = 0;

	/* 设置绑定参数：char */
	virtual void BindParam(int pos, const char* value) = 0;

	/* 设置绑定参数：char */
	virtual void BindParam(int pos, const std::string& value) = 0;

	/* 设置绑定参数：blob */
	virtual void BindBlobParam(int pos, const char* value) = 0;

	/* 设置绑定参数：blob */
	virtual void BindBlobParam(int pos, const std::string& value) = 0;

	/* 获取查询参数 */
	virtual const char* GetValue(int pos) = 0;

	/* 获取查询参数：char */
    virtual void GetValue(int pos, char* p) = 0;

    /* 获取查询参数：char */
    virtual void GetValue(int pos, std::string& value) = 0;

	/* 获取查询参数：int */
	virtual void GetValue(int pos, int& value) = 0;

	/* 获取查询参数：long */
	virtual void GetValue(int pos, long& value) = 0;

	/* 获取查询参数：long */
	virtual void GetValue(int pos, long long& value) = 0;

	/* 获取带绑定参数的SQL */
	virtual void GetSqlString(std::string& sql) = 0;

	/* 获取SQL */
	virtual const char* GetSql() = 0;

private:
	/* 禁止拷贝 */
	UDBSQL(const UDBSQL&);
	UDBSQL& operator=(const UDBSQL&);
};

class UDBException: public std::exception
{
public:
	/* 构造函数 */
	UDBException(int code, const char* info, int sqlCode, const char* sqlInfo, const char* sqlState) throw()
		:m_code(code)
		,m_sqlCode(sqlCode)
	{
		if(info) m_info = info;
		if(sqlInfo) m_sqlInfo = sqlInfo;
		if(sqlState) m_sqlState = sqlState;

		char scode[16];
		char sqcode[16];
        sprintf(scode, "%d", m_code);
        sprintf(sqcode, "%d", m_sqlCode);

		m_string.append("UDB Error no[").append(scode).append("] info[").append(m_info) \
				.append("] SQL Error no[").append(sqcode).append("] Info[").append(m_sqlInfo) \
				.append("] state[").append(m_sqlState).append("]");
	}

	/* 构造函数 */
	UDBException():m_code(0),m_sqlCode(0){}

	/* 析构函数 */
	virtual ~UDBException() throw() {}

	/* 获取内部错误码 */
	inline int GetErrorCode(){return m_code;}

	/* 获取内部错误信息 */
	inline const char* GetErrorInfo(){return m_info.c_str();}

	/* 获取SQL错误码 */
	inline int GetSqlCode(){return m_sqlCode;}

	/* 获取SQL错误信息 */
	inline const char* GetSqlInfo(){return m_sqlInfo.c_str();}

	/* 获取SQL语句状态 */
    inline const char* GetSqlState(){return m_sqlState.c_str();}

	/* 获取综合信息 */
    inline const char* ToString() {return m_string.c_str();}

	/* 获取异常信息 */
    virtual const char* what() const throw(){return m_string.c_str();}

private:
	int m_code;
	int m_sqlCode;
	std::string m_info;
	std::string m_sqlState;
	std::string m_sqlInfo;
	std::string m_string;
};

#endif // __DCUDB_H__
