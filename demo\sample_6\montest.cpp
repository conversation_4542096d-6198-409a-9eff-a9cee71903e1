#include "DCMon.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

int main(int argc, char* argv[])
{
    if(argc < 3)
	{
        printf("%s interval_ms  sock_type ip:port\n", argv[0]);
        return 1;
	}
	int ret = 0;
	const char* addr = NULL;
	int sock_type = 0;
    int interval_ms = atoi(argv[1]);
    if(interval_ms <= 0)
	{
		printf("interval_ms must more than zero\n");
        return 1;
	}

	sock_type = atoi(argv[2]);
    if(sock_type)
	{
		sock_type = 1;
	}

	if(argc > 3)
	{
		addr = argv[3];
	}

	DCMon* mon = DCMon::instance();

	ret = mon->init(interval_ms, sock_type, addr);
	if(ret < 0)
	{
        printf("init dcmon failed\n");
        return ret;
	}

	mon->head("BILLING", "CTPC", "SP");
    mon->group_set("net", "host", "127.0.0.1");
    mon->group_set("sp_net", "host", "127.0.0.1");
    mon->group_set("sm_sess", "host", "127.0.0.1");

	while(1)
	{
    	mon->cycle_array_inc("net", 0, "k1");
    	mon->cycle_array_inc("net", 0, "k2");
    	mon->cycle_array_inc("net", 0, "k7", "5");
    	mon->cycle_array_inc("net", 0, "k7", "10");
    	mon->cycle_array_inc("net", 0, "k7", "50");
    	mon->cycle_array_inc("net", 0, "k7", "100");

    	mon->cycle_array_inc("net", 1, "k1");
    	mon->cycle_array_inc("net", 1, "k2");
    	mon->cycle_array_inc("net", 1, "k7", "5");
    	mon->cycle_array_inc("net", 1, "k7", "10");
    	mon->cycle_array_inc("net", 1, "k7", "50");
    	mon->cycle_array_inc("net", 1, "k7", "100");

		mon->state_array_set("sp_net", 0, "link", NULL, 1L);
		mon->state_array_set("sp_net", 1, "link", NULL, 1L);
		mon->state_array_set("sp_net", 2, "link", NULL, 1L);

		mon->state_set("sm_sess", "voice", NULL, 10L);
		mon->state_set("sm_sess", "sms", NULL, 12L);
		mon->state_set("sm_sess", "data", NULL, 34L);

		usleep(10000);
	}
    return 0;
}
