#ifndef __DCMON_H__
#define __DCMON_H__

class DCMonImp;
class DCMon
{
	DCMonImp*	m_imp;

public:
	/***构造函数***/
	DCMon();

	/***析构函数***/
	~DCMon();

	/***可选单例模式***/
	static DCMon* instance();

	/***初始化函数
	****发送间隔：interval_ms
	****传输类型：sock_type，0-UDP,1-TCP
	****服务地址：servaddr=IP:PORT
	***/
	int init(int interval_ms, int sock_type = 0, const char* servaddr = 0L);

    /***设置通用指标头部信息***/
	void head(const char*system, const char*subsys, const char*module);

	/***设置指标头部信息***/
	void head_set(const char*name, const char*value);

	/***设置指标组头部信息***/
	void group_set(const char* group, const char* name, const char* value);

	/***设置单组指标增量值***/
	void cycle_inc(const char* group, const char* k, const char* ki=0L, long value=1);

	/***设置单组指标当前值***/
	void cycle_set(const char* group, const char* k, const char* ki=0L, long value=0);

	/***设置单组指标整数状态值***/
	void state_set(const char* group, const char* k, const char* ki=0L, long value=0);

	/***设置单组指标浮点状态值***/
	void state_set(const char* group, const char* k, const char* ki=0L, float value=0);

	/***设置多组指标增量值***/
	void cycle_array_inc(const char* group, int id, const char* k, const char* ki=0L, long value=1);

	/***设置多组指标当前值***/
	void cycle_array_set(const char* group, int id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标整数状态值***/
	void state_array_set(const char* group, int id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标浮点状态值***/
	void state_array_set(const char* group, int id, const char* k, const char* ki=0L, float value=0);

	/***设置多组指标增量值***/
	void cycle_array_inc(const char* group, const char* id, const char* k, const char* ki=0L, long value=1);

	/***设置多组指标当前值***/
	void cycle_array_set(const char* group, const char* id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标整数状态值***/
	void state_array_set(const char* group, const char* id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标浮点状态值***/
	void state_array_set(const char* group, const char* id, const char* k, const char* ki=0L, float value=0);
};

#endif // __DCMON_H__
