#include "DCBasePlugin.h"
#include <stdio.h>
#include <utility>

class SamAdd : public DCBasePlugin
{
public:
	SamAdd(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)
        :DCBasePlugin(category,func,version)
	{
	}

	virtual ~SamAdd()
	{

	}

protected:
	virtual int init()
	{
		return 0;
	}

	virtual int process(void* input, void* output)
	{
		printf("do SamAdd \n");
        std::pair<int, int>& in = *(std::pair<int, int>*)input;
		int& ot = *(int*)output;

		ot = in.first + in.second;

		return 0;
	}

private:

};

DYN_PLUGIN_CREATE(SamAdd, "FUNC_ALGO", "FC_Add", "1.0.0")

