#include "DCSeriaOp.h"
#include <vector>
#include <string>
/********************************DCBinTransOp****************************************/
class DCBinTransOp
{
public:
    static void encode(const uint8_t* data, size_t size, std::vector<uint8_t>& out)
    {
		if(out.size() + size +1 > out.capacity())
		{
			out.reserve(((out.size() + size)/4096+1)*4096);
		}
        for(size_t i = 0; i < size; i++)
		{
			switch(data[i])
			{
				case 0x0  :{
					out.push_back((uint8_t)0x7F);
					break;
				}
				case 0x7D :
                case 0x7F :{
					out.push_back((uint8_t)0x7D);
					out.push_back(data[i]);
					break;
                }
                default:{
                	out.push_back(data[i]);
					break;
                }
			}
		}
		if (size) 
		{
			out[out.size()] = 0x0;
		}
    }

    static void decode(const uint8_t* data, size_t size, std::vector<uint8_t>& out)
    {
              if(out.capacity() < size)
              {
                    out.reserve(size);
              }
		
		bool hit = false;
		for(size_t i = 0; i < size; i++)
		{
			switch(data[i])
			{
				case 0x7F  :{
					if(hit){
						out.push_back(data[i]);
						hit = false;
					}else{
						out.push_back((uint8_t)0x0);
					}
					break;
				}
                case 0x7D :{
                    if(hit){
						out.push_back(data[i]);
						hit = false;
					}else{
						hit = true;
					}
					break;
                }
                default:{
                	out.push_back(data[i]);
					break;
                }
			}
		}
    }
};

/********************************DCMemInputStream****************************************/

class DCMemInputStream: public avro::InputStream
{
public:
	DCMemInputStream(ESeriaType type)
		:data_(NULL)
		,size_(0)
		,curLen_(0)
		,type_(type)
	{
	}

	virtual ~DCMemInputStream()
	{
	}

	virtual bool next(const uint8_t** data, size_t* len)
	{
		if (curLen_ == size_) {
            return false;
        }
        *data = &data_[curLen_];
        *len = size_ - curLen_;
        curLen_ = size_;
        return true;
	}

	virtual void backup(size_t len)
	{
		curLen_ -= len;
	}

	virtual void skip(size_t len)
	{
		if (len > (size_ - curLen_)) {
            len = size_ - curLen_;
        }
        curLen_ += len;
	}

	virtual size_t byteCount() const
	{
		return curLen_;
	}

	void reset(const uint8_t *data, size_t len)
	{
		if(type_ == ESeriaBinString)
		{
			in_.clear();
			DCBinTransOp::decode(data,len, in_);
			data_ = &in_[0];
			size_ = in_.size();
		}
		else
		{
			data_ = data;
			size_ = len;
		}
		curLen_ = 0;
	}

private:
	const uint8_t* data_;
    size_t size_;
    size_t curLen_;
    ESeriaType type_;
    std::vector<uint8_t> in_;
};

/********************************DCMemOutputStream****************************************/

class DCMemOutputStream: public avro::OutputStream
{
public:
	DCMemOutputStream(ESeriaType type, size_t chunkSize)
		:type_(type)
		,chunkSize_(chunkSize)
		,available_(0)
		,byteCount_(0)
		,seqCount_(0)
		,transCount_(0)
	{
	}

	virtual ~DCMemOutputStream()
	{

	}

	virtual bool next(uint8_t** data, size_t* len)
	{
		if (available_ == 0) {
            more();
            available_ = chunkSize_;
        }
        *data = &data_[seqCount_  - available_];
        *len = available_;
        byteCount_ += available_;
        available_ = 0;
        return true;
	}

	virtual void backup(size_t len)
	{
		available_ += len;
        byteCount_ -= len;
	}

	virtual uint64_t byteCount() const
	{
		return byteCount_;
	}

	virtual void flush()
	{
		if( (type_ == ESeriaBinString) && (transCount_ < byteCount_) ){
			DCBinTransOp::encode(&data_[transCount_], byteCount_-transCount_, out_);
			transCount_ = byteCount_;
		}
	}

	void reset()
	{
		available_ = 0;
		byteCount_ = 0;
		seqCount_ = 0;
		transCount_ = 0;
		data_.clear();
		out_.clear();
	}

	void more()
	{
		seqCount_+=chunkSize_;
		if( seqCount_ > data_.capacity())
		{
			data_.reserve(seqCount_);
		}
		data_.resize(seqCount_);
	}

	size_t  size()
	{
		return type_ == ESeriaBinString ? out_.size() : byteCount_;
	}

	uint8_t* data()
	{
		return type_ == ESeriaBinString ? &out_[0] : &data_[0];
	}

private:
	ESeriaType type_;
	const size_t chunkSize_;
    size_t available_;
    size_t byteCount_;
    size_t seqCount_;
    size_t transCount_;
    std::vector<uint8_t> data_;
    std::vector<uint8_t> out_;
};

/********************************DCSeriaEncoder****************************************/
DCSeriaEncoder::DCSeriaEncoder(ESeriaType type)
	:m_out(new DCMemOutputStream(type,4096))
{
	m_e = avro::binaryEncoder();
	m_e->init(*m_out);
}

DCSeriaEncoder::~DCSeriaEncoder()
{

}

size_t DCSeriaEncoder::size()
{
	return static_cast<DCMemOutputStream*>(m_out.get())->size();
}

const uint8_t* DCSeriaEncoder::data()
{
	return static_cast<DCMemOutputStream*>(m_out.get())->data();
}

void DCSeriaEncoder::clear()
{
	static_cast<DCMemOutputStream*>(m_out.get())->reset();
}

/********************************DCSeriaDecoder****************************************/

DCSeriaDecoder::DCSeriaDecoder(ESeriaType type)
	:m_in(new DCMemInputStream(type))
{
	m_e = avro::binaryDecoder();
	m_e->init(*m_in);
}

DCSeriaDecoder::~DCSeriaDecoder()
{
}

void DCSeriaDecoder::set(const uint8_t* data, size_t size)
{
	m_e->init(*m_in);
	static_cast<DCMemInputStream*>(m_in.get())->reset(data, size);
}

void DCSeriaDecoder::set(const std::vector<uint8_t>& data)
{
	set(&data[0], data.size());
}

/********************************DCSeriaPrinter****************************************/
DCSeriaPrinter::DCSeriaPrinter()
	:m_out(new DCMemOutputStream(ESeriaBinary,4096))
{
	m_p = avro::binaryPrinter();
	m_p->init(*m_out);
}

DCSeriaPrinter::~DCSeriaPrinter()
{

}

size_t DCSeriaPrinter::size()
{
	return static_cast<DCMemOutputStream*>(m_out.get())->size();
}

const char* DCSeriaPrinter::data()
{
	return (const char*) static_cast<DCMemOutputStream*>(m_out.get())->data();
}

void DCSeriaPrinter::clear()
{
	static_cast<DCMemOutputStream*>(m_out.get())->reset();
}

/********************************HexEncode/HexDecode****************************************/
static char s_hex[16]={'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};
std::string HexEncode(const uint8_t* data, size_t size)
{
	std::string out = "";
    out.reserve(size*2);
    for(size_t i = 0; i < size; i++)
	{
        out+=s_hex[data[i]>>4];
        out+=s_hex[data[i]&0x0F];
	}
	return out;
}

static inline uint8_t hex2dec(char ch)
{
	uint8_t ret = 0;
	if(ch >= '0' && ch <='9'){
		ret = ch-'0';
	}
	else if(ch >= 'a' && ch <='f'){
        ret = ch-'a'+10;
	}
	else if(ch >= 'A' && ch <='F'){
		ret = ch-'A'+10;
	}
	return ret;
}

std::vector<uint8_t> HexDecode(const char* data, size_t size)
{
    uint8_t hex[2];
	std::vector<uint8_t> out;
	if(size%2)size--;
    out.reserve(size);
    for(size_t i = 0; i< size; i+=2)
	{
		hex[0] = hex2dec(data[i]);
		hex[1] = hex2dec(data[i+1]);
		out.push_back((hex[0]<<4)+hex[1]);
	}
	return out;
}

/********************************HexEncode/HexDecode****************************************/
