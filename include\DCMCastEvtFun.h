#ifndef __DCMCASTEVTFUN_H__
#define __DCMCASTEVTFUN_H__
#include "DCMCastManer.h"

struct LogEventFun : public DCMCastManer::EventFun
{
    DCMCastManer* m_mcm;
    
    LogEventFun(DCMCastManer* mc):m_mcm(mc){}
        
    virtual void callback(const char*name, const char* value);

    void register_event(const char* mod);
};

class DCRFData;
struct BoltEventFun : public DCMCastManer::EventFun
{
    DCMCastManer* m_mcm;
    DCRFData*     m_drf;
	
    BoltEventFun(DCMCastManer* mc, DCRFData* drf):m_mcm(mc),m_drf(drf){}
        
    virtual void callback(const char*name, const char* value);

    void register_event(const char* topology, int taskid, const char* mod);
};

#endif // __DCMCASTEVTFUN_H__

