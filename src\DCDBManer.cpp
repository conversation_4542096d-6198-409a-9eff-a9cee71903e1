#include "DCDBManer.h"
#include "DCLogMacro.h"
#include "DCPerfStatistic.h"
#include <set>
#include <map>
#include <string>
#include <string.h>
#include <tinyxml.h>
#include <dlfcn.h>
#include "encryptCpt.h"
#include "DCParseXml.h"


typedef UDBConnection* (*FUNCDBCREATE)(const char* name);
typedef int (*FUNCMODINIT)(const char* args);
/*----------------------------------------HelperFunction------------------------------------------------*/
static void split_string(const std::string& str, const std::string& sep, std::vector<std::string>& vec)
{
	size_t head = 0;
	size_t tail = 0;
	while(tail < str.size())
	{
		tail = str.find(sep, head);
		if(tail == std::string::npos)
		{
			vec.push_back(str.substr(head));
			tail = str.size();
		}
		else
		{
			vec.push_back(str.substr(head, tail-head));
			head=tail+sep.size();
		}
	}
}

static inline unsigned int string_hash(const char* str)
{
	// BKDR Hash 
	unsigned int seed = 131; // 31 131 1313 13131 131313 etc..  
	unsigned int hash = 0;  

	while (*str)  
	{  
		hash = hash * seed + (*str++);  
	}  

	return (hash & 0x7FFFFFFF);  
}

static inline void tolowers(std::string& s)
{
	for(std::string::iterator it= s.begin(); it != s.end(); it++)
	{
		*it = tolower(*it);
	}
}

static const char* sstrcasestr(const char* str1, const char* str2)
{
	std::string s1(str1);
	std::string s2(str2);
	tolowers(s1);
	tolowers(s2);
	return strstr(s1.c_str(), s2.c_str());
}

/* skip(s, "_", 2, 1) skip m item from nth with zero start*/
static std::string fn_skip(const std::string& s, const std::string& sep, int index, int size)
{
	if( s.empty() || sep.empty() || size < 1)
	{
		return s;
	}
	size_t tail = 0;
	size_t ltail = 0;
	size_t head = 0;
	size_t num = 0;
	std::string ss;
	if(index<0)index = 0;
	while(tail < s.size())
	{
		ltail = tail;
		tail = s.find(sep, head);
		if(tail == std::string::npos)
		{
			tail = s.size();
		}
		else
		{
			head = tail + sep.size();
		}
		if(num == index && index > 0)
		{
			ss = s.substr(0, ltail);
		}
		num++;
		if(num == index + size)
		{
			if(index ==0 && tail != s.size())
			{
				tail+=sep.size();
			}
			ss += s.substr(tail);
			break;
		}
	}
	return ss;
}

/* get dkey from original key*/
static inline std::string get_dkey(const std::string&s, const char*fmt)
{
	if(!fmt||!fmt[0])
	{
		return s;
	}
	if(!strcmp(fmt, "[K];%"))
	{
		size_t pos = s.rfind(';');
		if(pos != std::string::npos)
		{
			return s.substr(0, pos);
		}
	}
	return s;
}

static inline std::string& trim(std::string& s)
{
	size_t pos = s.find_first_not_of(' ');
	if(pos)
	{
		s.erase(0, pos);
	}
	pos = s.find_last_not_of(' ');
	if(pos != std::string::npos)
	{
		s.erase(pos+1);
	}
	return s;
}

/*----------------------------------------UDBConnectionHK------------------------------------------------*/
/***---used for exec time collect---***/
struct DCTimeSQLCollect
{
	bool                      m_nr;
	DCPerfTimeStats* 	m_pos[2];
	struct timeval	m_begin;
	struct timeval	m_end;

	DCTimeSQLCollect(DCPerfTimeStats* pos1, DCPerfTimeStats* pos2, bool nr = true)
		:m_nr(nr)
	{
		m_pos[0] = pos1;
		m_pos[1] = pos2;
		gettimeofday(&m_begin, NULL);
	}

	~DCTimeSQLCollect()
	{
		gettimeofday(&m_end, NULL);
		long usec = (m_end.tv_sec - m_begin.tv_sec)*1000000 + m_end.tv_usec - m_begin.tv_usec;
		DCPerfStatistic::set_time(m_pos[0], usec, m_nr);
		DCPerfStatistic::set_time(m_pos[1], usec, m_nr);
	}
};

class UDBSQLHK;
class UDBConnectionHK : public UDBConnection
{
public:
	UDBConnectionHK(UDBConnection* conn, DCPerfStatistic* stat, bool bdiv)
		:UDBConnection(conn->category(), conn->name(), conn->version())
		,m_con(conn)
		,m_stat(stat)
		,m_bdiv(bdiv)
	{
		std::string sn = std::string("D.")+ UDBConnection::name();
		m_ts = m_stat->get_position(sn.c_str());
	}

	virtual ~UDBConnectionHK()
	{
		std::string sn = std::string("D.")+ UDBConnection::name();
		m_stat->del_position(sn.c_str());
		delete m_con;
	}

	virtual int Connect(const char* str, const char* env = 0)
	{
		return m_con->Connect(str, env);
	}

	virtual int Destroy()
	{
		return m_con->Destroy();
	}

	virtual int Reset(const char* str = 0)
	{
		return m_con->Reset(str);
	}

	virtual void Commit()
	{
		m_con->Commit();
	}

	virtual void Rollback()
	{
		try
		{
			m_con->Rollback();
		}catch(UDBException& e)
		{
			// do nothing
		}
	}

	virtual UDBSTATE State(bool force=false)
	{
		return m_con->State(force);
	}

	virtual const char* GetConStr()
	{
		return m_con->GetConStr();
	}

	virtual UDBSQL* Stmt(const char* sql)
	{
		return StmtN("None", sql);
	}

	/// add helper func
	inline UDBSQL* StmtN(const char* name, const char* sql);

	bool isDiv()
	{
		return m_bdiv;
	}

public:
	DCPerfStatistic*		m_stat;
	DCPerfTimeStats*		m_ts;

private:
	UDBConnection*		m_con;
	bool				m_bdiv;
};

class UDBSQLHK : public UDBSQL
{
public:
	UDBSQLHK(UDBConnectionHK*db, UDBSQL*sql, const char* name)
		:m_db(db)
		,m_sql(sql)
		,m_name(name)
	{
		std::string sn = std::string("S.")+ m_name;
		m_ts = m_db->m_stat->get_position(sn.c_str());
	}

	virtual ~UDBSQLHK()
	{
		if(strcmp(m_name.c_str(), "None") != 0)
		{
			std::string sn = std::string("S.")+ m_name;
			m_db->m_stat->del_position(sn.c_str());
		}
		delete m_sql;
	}

	virtual UDBSTATE State()
	{
		return m_sql->State();
	}

	virtual UDBSTMT  StmtType()
	{
		return m_sql->StmtType();
	}

	virtual UDBConnection* Connection()
	{
		return m_db;
	}

	virtual void Reset()
	{
		m_sql->Reset();
	}

	virtual void DivTable(const char* key)
	{
		m_sql->DivTable(key);
	}

	virtual void Execute()
	{
		DCTimeSQLCollect coll(m_ts, m_db->m_ts);
		if(m_sql->State() == UDBS_STMT_INVALID)
		{
			m_sql->Reset();
		}
		m_sql->Execute();
	}

	virtual bool Next()
	{
		DCTimeSQLCollect coll(m_ts, m_db->m_ts, false);
		return m_sql->Next();
	}

	virtual void Close()
	{
		m_sql->Close();
	}

	virtual int  GetRowCount()
	{
		return m_sql->GetRowCount();
	}

	virtual void SetIntParam(int pos)
	{
		m_sql->SetIntParam(pos);
	}

	virtual void SetLongParam(int pos)
	{
		m_sql->SetLongParam(pos);
	}

	virtual void SetCharParam(int pos)
	{
		m_sql->SetCharParam(pos);
	}

	virtual void SetBlobParam(int pos)
	{
		m_sql->SetBlobParam(pos);
	}

	virtual void SetBindParam(const std::string& para)
	{
		m_sql->SetBindParam(para);
	}

	virtual void GetBindParam(std::string& para)
	{
		m_sql->GetBindParam(para);
	}

	virtual void UnBindParam()
	{
		m_sql->UnBindParam();
	}

	virtual void BindParam(int pos, int value)
	{
		m_sql->BindParam(pos, value);
	}

	virtual void BindParam(int pos, long value)
	{
		m_sql->BindParam(pos, value);
	}

	virtual void BindParam(int pos, long long value)
	{
		m_sql->BindParam(pos, value);
	}

	virtual void BindParam(int pos, const char* value)
	{
		m_sql->BindParam(pos, value);
	}

	virtual void BindParam(int pos, const std::string& value)
	{
		m_sql->BindParam(pos, value);
	}

	virtual void BindBlobParam(int pos, const char* value)
	{
		m_sql->BindBlobParam(pos, value);
	}

	virtual void BindBlobParam(int pos, const std::string& value)
	{
		m_sql->BindBlobParam(pos, value);
	}

	virtual const char* GetValue(int pos)
	{
		return m_sql->GetValue(pos);
	}

	virtual void GetValue(int pos, char* p)
	{
		m_sql->GetValue(pos, p);
	}

	virtual void GetValue(int pos, std::string& value)
	{
		m_sql->GetValue(pos, value);
	}

	virtual void GetValue(int pos, int& value)
	{
		m_sql->GetValue(pos, value);
	}

	virtual void GetValue(int pos, long& value)
	{
		m_sql->GetValue(pos, value);
	}

	virtual void GetValue(int pos, long long& value)
	{
		m_sql->GetValue(pos, value);
	}

	virtual void GetSqlString(std::string& sql)
	{
		m_sql->GetSqlString(sql);
	}

	virtual const char* GetSql()
	{
		return m_sql->GetSql();
	}

	inline std::vector<UDBSQL*> vSql();

private:
	UDBConnectionHK*	m_db;
	UDBSQL* 			m_sql;
	DCPerfTimeStats*		m_ts;
	std::string			m_name;
};

inline UDBSQL* UDBConnectionHK::StmtN(const char* name, const char* sql)
{
	UDBSQL* dsql = NULL;
	if(m_bdiv)
	{
		dsql = m_con->Stmt(sql);
	}
	else if(!strstr(sql, "[P]"))
	{
		dsql = m_con->Stmt(sql);
	}
	else
	{
		std::string s1(sql);
		std::size_t pos = s1.find("[P]");
		while(pos != std::string::npos)
		{
			s1.erase(pos, 3);
			pos = s1.find("[P]", pos);
		};
		dsql = m_con->Stmt(s1.c_str());
	}

	if(!dsql) return dsql;

	UDBSQLHK* shk = new UDBSQLHK(this, dsql, name);
	return shk;
}

/*----------------------------------------UDBConnectionHA------------------------------------------------*/
/***---used for HA connection---***/
class UDBSQLHA;
class UDBConnectionHA : public UDBConnection
{
public:
	UDBConnectionHA(const char* category, const char* name, const char* version, FUNCDBCREATE crt, DCDBMODE& mode)
		:UDBConnection(category, name, version)
		,m_mode(mode)
		,m_cur(0)
		,m_crt(crt)
	{
		if(m_mode == DBME_SLAVE || m_mode == DBMN_SLAVE)
		{
			m_cur = 1;
		}
		memset(&m_db, 0, sizeof(m_db));
	}

	virtual ~UDBConnectionHA()
	{
		delete m_db[0];
		delete m_db[1];
	}

	virtual int Connect(const char* str, const char* env = 0)
	{
		m_sconn = str;
		if(env) m_env = env;
		std::string con[2];
		size_t found = m_sconn.find("!!!");
		con[0] = m_sconn.substr(0, found);
		con[1] = m_sconn.substr(found+3);

		for(int i=0; i<2; i++)
		{
			if((m_mode == DBMN_MASTER && i == 1) || (m_mode == DBMN_SLAVE && i == 0))
			{
				continue;
			}

			try
			{
				m_db[i] = m_crt(UDBConnection::name());
				if(m_db[i])
				{
					m_db[i]->Connect(con[i].c_str(), env);

					DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "Connect to db[%s] ConStr[%s] env[%s] success", \
						UDBConnection::name(), con[i].c_str(), m_env.c_str());
				}
			}
			catch(UDBException& e)
			{
				delete m_db[i];
				m_db[i] = NULL;

				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "Connect to db[%s] ConStr[%s] env[%s] failed:[%s]", \
					UDBConnection::name(), con[i].c_str(), m_env.c_str(), e.ToString());
			}
		}

		if(m_mode == DBME_MASTER || m_mode == DBMN_MASTER)
		{
			m_cur = 0;
			if(!m_db[0] && m_db[1])
			{
				m_cur = 1;
			}
		}
		if(m_mode == DBME_SLAVE || m_mode == DBMN_SLAVE)
		{
			m_cur = 1;
			if(m_db[0] && !m_db[1])
			{
				m_cur = 0;
			}
		}
		if(!m_db[0] && !m_db[1])
		{
			char buf[80];
			sprintf(buf,"UDBConnectionHA::Connect failed, category[%s], version[%s], name[%s]", category(), version(), name());
			throw UDBException(-1, buf, 0, "","");
		}
		return 0;
	}

	virtual int Destroy()
	{
		for(int i=0; i< 2; i++)
		{
			if(m_db[i]) m_db[i]->Destroy();
		}
		return 0;
	}

	virtual int Reset(const char* str = 0)
	{
		int ret = 0;
		std::string con[2];
		if(str && m_sconn != str)
		{
			m_sconn = str;
		}
		size_t found = m_sconn.find("!!!");
		con[0] = m_sconn.substr(0, found);
		con[1] = m_sconn.substr(found+3);

		if(m_mode == DBMN_MASTER || m_mode == DBMN_SLAVE)
		{
			m_cur =  m_mode==DBMN_MASTER ? 0 : 1;
			if(m_db[m_cur])
			{
				if(con[m_cur] != m_db[m_cur]->GetConStr())
				{
					m_db[m_cur]->Reset(con[m_cur].c_str());
				}
				else if(m_db[m_cur]->State() != UDBS_DB_LINK)
				{
					m_db[m_cur]->Reset(NULL);
				}
			}
			else
			{
				try
				{
					m_db[m_cur] = m_crt(UDBConnection::name());
					if(m_db[m_cur])
					{
						m_db[m_cur]->Connect(con[m_cur].c_str(), m_env.c_str());
						ret = fixSQL(m_cur);
						if(ret < 0)
						{
							delete m_db[m_cur];
							m_db[m_cur] = NULL;

							DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "fixSQL for db[%s] ConStr[%s] env[%s] failed", \
								UDBConnection::name(), con[m_cur].c_str(), m_env.c_str());
						}
					}
				}catch(UDBException& e)
				{
					delete m_db[m_cur];
					m_db[m_cur] = NULL;
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "exception for db[%s] ConStr[%s] env[%s] exp[%s]", \
						UDBConnection::name(), con[m_cur].c_str(), m_env.c_str(), e.ToString());
					return -1;
				}
			}
			if(!m_db[m_cur] || m_db[m_cur]->State() != UDBS_DB_LINK)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "unlink db[%s] ConStr[%s]", UDBConnection::name(), con[m_cur].c_str());
				return -1;
			}
		}
		else
		{
			for(int i=0; i < 2; i++)
			{
				if(m_db[i])
				{
					if(con[i] !=m_db[i]->GetConStr())
					{
						m_db[i]->Reset(con[i].c_str());
					}
					else if(m_db[i]->State() != UDBS_DB_LINK)
					{
						m_db[i]->Reset(NULL);
					}
				}
				else
				{
					try
					{
						m_db[i] = m_crt(UDBConnection::name());
						if(m_db[i])
						{
							m_db[i]->Connect(con[i].c_str(), m_env.c_str());
							ret = fixSQL(i);
							if(ret < 0)
							{
								delete m_db[i];
								m_db[i] = NULL;

								DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "fixSQL for db[%s] ConStr[%s] env[%s] failed", \
									UDBConnection::name(), con[i].c_str(), m_env.c_str());
							}
						}
					}catch(UDBException& e)
					{
						delete m_db[i];
						m_db[i] = NULL;

						DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "exception for db[%s] ConStr[%s] env[%s] exp[%s]", \
							UDBConnection::name(), con[i].c_str(), m_env.c_str(), e.ToString());
					}
				}
			}

			if((!m_db[0] || m_db[0]->State() != UDBS_DB_LINK)
				&&(!m_db[1] || m_db[1]->State() != UDBS_DB_LINK))
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "unlink db[%s] ConStr1[%s], ConStr2[%s]", \
					UDBConnection::name(), con[0].c_str(), con[1].c_str());
				return -1;
			}

			m_cur = m_mode==DBME_MASTER ? 0 : 1;
			int k = (m_cur+1)%2;
			if((!m_db[m_cur] || m_db[m_cur]->State() != UDBS_DB_LINK)
				&&(m_db[k] && m_db[k]->State() == UDBS_DB_LINK))
			{
				m_cur = k;
			}
		}

		return ret;
	}

	virtual void Commit()
	{
		if(m_db[m_cur]) m_db[m_cur]->Commit();
	}

	virtual void Rollback()
	{
		if(m_db[m_cur]) m_db[m_cur]->Rollback();
	}

	virtual UDBSTATE State(bool force=false)
	{
		if(m_mode == DBMN_MASTER || m_mode == DBMN_SLAVE)
		{
			if(m_db[m_cur])
			{
				return m_db[m_cur]->State(force);
			}
			return UDBS_NONE;
		}

		int num = 0;
		for(int i=0; i<2; i++)
		{
			if(m_db[i] && m_db[i]->State(force) == UDBS_DB_LINK)
			{
				num++;
			}
		}
		if(num == 2) return UDBS_DB_LINK;
		if(num == 1) return UDBS_DB_ANY_LINK;
		return UDBS_DB_UNLINK;
	}

	virtual UDBSQL* Stmt(const char* sql);

	virtual const char* GetConStr()
	{
		return m_sconn.c_str();
	}

private:
	int fixSQL(int id);

public:
	DCDBMODE&       m_mode;
	int				m_cur;
	FUNCDBCREATE	m_crt;
	UDBConnection* 	m_db[2];
	std::string 	m_sconn;
	std::string		m_env;
	std::set<UDBSQLHA*> m_sqls;
};

class UDBSQLHA : public UDBSQL
{
public:
	UDBSQLHA(UDBConnectionHA* db)
		:m_dbHA(db)
		,m_cur(db->m_cur)
	{
		memset(&m_sql, 0x0, sizeof(m_sql));
	}

	virtual ~UDBSQLHA()
	{
		for(int i=0; i< 2; i++)
		{
			delete m_sql[i];
		}
		m_dbHA->m_sqls.erase(this);
	}

	virtual UDBSTATE State()
	{
		return m_sql[m_cur]->State();
	}

	virtual UDBSTMT  StmtType()
	{
		return m_sql[m_cur]->StmtType();
	}

	virtual UDBConnection* Connection()
	{
		return m_dbHA;
	}

	virtual void Reset()
	{
		m_sql[m_cur]->Reset();
	}

	virtual void DivTable(const char* key)
	{
		m_sql[m_cur]->DivTable(key);
	}

	virtual void Execute()
	{
		m_sql[m_cur]->Execute();
	}

	virtual bool Next()
	{
		return m_sql[m_cur]->Next();
	}

	virtual void Close()
	{
		m_sql[m_cur]->Close();
	}

	virtual int  GetRowCount()
	{
		return m_sql[m_cur]->GetRowCount();
	}

	virtual void SetIntParam(int pos)
	{
		for(int i=0; i< 2; i++)
		{
			if(m_sql[i]) m_sql[i]->SetIntParam(pos);
		}
	}

	virtual void SetLongParam(int pos)
	{
		for(int i=0; i< 2; i++)
		{
			if(m_sql[i]) m_sql[i]->SetLongParam(pos);
		}
	}

	virtual void SetCharParam(int pos)
	{
		for(int i=0; i< 2; i++)
		{
			if(m_sql[i]) m_sql[i]->SetCharParam(pos);
		}
	}

	virtual void SetBlobParam(int pos)
	{
		for(int i=0; i< 2; i++)
		{
			if(m_sql[i]) m_sql[i]->SetBlobParam(pos);
		}
	}

	virtual void SetBindParam(const std::string& para)
	{
		for(int i=0; i< 2; i++)
		{
			if(m_sql[i]) m_sql[i]->SetBindParam(para);
		}
	}

	virtual void GetBindParam(std::string& para)
	{
		return m_sql[m_cur]->GetBindParam(para);
	}

	virtual void UnBindParam()
	{
		m_sql[m_cur]->UnBindParam();
	}

	virtual void BindParam(int pos, int value)
	{
		m_sql[m_cur]->BindParam(pos, value);
	}

	virtual void BindParam(int pos, long value)
	{
		m_sql[m_cur]->BindParam(pos, value);
	}

	virtual void BindParam(int pos, long long value)
	{
		m_sql[m_cur]->BindParam(pos, value);
	}

	virtual void BindParam(int pos, const char* value)
	{
		m_sql[m_cur]->BindParam(pos, value);
	}

	virtual void BindParam(int pos, const std::string& value)
	{
		m_sql[m_cur]->BindParam(pos, value);
	}

	virtual void BindBlobParam(int pos, const char* value)
	{
		m_sql[m_cur]->BindBlobParam(pos, value);
	}

	virtual void BindBlobParam(int pos, const std::string& value)
	{
		m_sql[m_cur]->BindBlobParam(pos, value);
	}

	virtual const char* GetValue(int pos)
	{
		return m_sql[m_cur]->GetValue(pos);
	}

	virtual void GetValue(int pos, char* p)
	{
		m_sql[m_cur]->GetValue(pos, p);
	}

	virtual void GetValue(int pos, std::string& value)
	{
		m_sql[m_cur]->GetValue(pos, value);
	}

	virtual void GetValue(int pos, int& value)
	{
		m_sql[m_cur]->GetValue(pos, value);
	}

	virtual void GetValue(int pos, long& value)
	{
		m_sql[m_cur]->GetValue(pos, value);
	}

	virtual void GetValue(int pos, long long& value)
	{
		m_sql[m_cur]->GetValue(pos, value);
	}

	virtual void GetSqlString(std::string& sql)
	{
		m_sql[m_cur]->GetSqlString(sql);
	}

	virtual const char* GetSql()
	{
		return m_sql[m_cur]->GetSql();
	}

	int dofix(int id)
	{
		int x = (id+1)%2;
		if(!m_sql[x] || !m_dbHA->m_db[id])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "empty sql or db");
			return -1;
		}
		const char* sql = NULL;
		std::string para;
		try
		{
			sql = m_sql[x]->GetSql();
			m_sql[x]->GetBindParam(para);

			m_sql[id] = m_dbHA->m_db[id]->Stmt(sql);
			if(!m_sql[id])
			{
				return -1;
			}

			m_sql[id]->SetBindParam(para);
		}
		catch(UDBException&e)
		{
			delete m_sql[id];
			m_sql[id] = NULL;

			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "prepare sql failed, db[%s], ConStr[%s], param[%s], sql[%s], exp[%s]", \
				m_dbHA->m_db[id]->name(), m_dbHA->m_db[id]->GetConStr(), para.c_str(), sql, e.ToString());
			return -1;
		}
		return 0;
	}

public:
	int&				m_cur;
	UDBSQL*				m_sql[2];
	UDBConnectionHA* 	m_dbHA;
};

UDBSQL* UDBConnectionHA::Stmt(const char* sql)
{
	UDBSQL* smt[2] = {NULL};
	if(m_mode == DBMN_MASTER || m_mode == DBMN_SLAVE)
	{
		if(m_db[m_cur])
		{
			smt[m_cur] = m_db[m_cur]->Stmt(sql);
		}
		if(!smt[m_cur])return NULL;
	}
	else
	{
		UDBException exp;
		for(int i=0, j=2; i< j; i++)
		{
			int x = (m_cur+i)%2;
			int y = (x+i)%2;
			try
			{
				if(m_db[x])
				{
					smt[x] = m_db[x]->Stmt(sql);
				}
			}catch(UDBException&e)
			{
				if(i==0 && m_db[x]->State() == UDBS_DB_LINK)
				{
					throw e;
				}
				else
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "db[%s], ConStr[%s], exp[%s]", \
						m_db[x]->name(), m_db[x]->GetConStr(), e.ToString());
				}
				if(i==0)exp = e;
			}
			if(!smt[x] && !smt[y] && m_db[y] && m_db[y]->State(true) == UDBS_DB_LINK)
			{
				m_cur = y;
				i = -1;
				j = 1;
			}
			else if(!smt[x])
			{
				break;
			}
		}
		if(!smt[0] && !smt[1])
		{
			throw exp;
		}
	}

	UDBSQLHA* sha = new UDBSQLHA(this);
	sha->m_sql[0] = smt[0];
	sha->m_sql[1] = smt[1];
	sha->m_cur = m_cur;
	m_sqls.insert(sha);
	return sha;
}

int UDBConnectionHA::fixSQL(int id)
{
	int ret = 0;
	std::set<UDBSQLHA*>::iterator it;
	for(it = m_sqls.begin(); it != m_sqls.end(); it++)
	{
		ret = (*it)->dofix(id);
		if(ret < 0)
		{
			return ret;
		}
	}
	return ret;
}

/*----------------------------------------UDBConnectionDIV------------------------------------------------*/
/***---used for the division of database and tables---***/

class UDBSQLDIV;
class UDBConnectionDIV : public UDBConnection
{
public:
	struct DCDivision
	{
		std::string name;
		int         sdb;
		int			ntbl;
		int			method;		// 0 hash, 1 mod
		int			mon;
	};
public:
	UDBConnectionDIV(const char* category, const char* name, const char* version, std::vector<UDBConnection*>& vdb, std::vector<DCDivision>& div)
		:UDBConnection(category, name, version)
		,m_cur(0)
		,m_vdb(vdb)
		,m_div(div)
	{
		for(unsigned int i = 0; i < m_div.size(); i++)
		{
			tolowers(m_div[i].name);
		}
	}

	virtual ~UDBConnectionDIV()
	{
		for(unsigned int i = 0; i<m_vdb.size(); i++)
		{
			delete m_vdb[i];
		}
	}

	virtual int Connect(const char* str, const char* env = 0)
	{
		return 0;
	}

	virtual int Destroy()
	{
		for(unsigned i = 0; i < m_vdb.size(); i++)
		{
			if(m_vdb[i]) m_vdb[i]->Destroy();
		}
		return 0;
	}

	virtual int Reset(const char* str = 0)
	{
		int ret = 0;
		for(unsigned i = 0; i < m_vdb.size(); i++)
		{
			ret += m_vdb[i]->Reset(str);
		}
		return ret;
	}

	virtual void Commit()
	{
		m_vdb[m_cur]->Commit();
	}

	virtual void Rollback()
	{
		m_vdb[m_cur]->Rollback();
	}

	virtual UDBSTATE State(bool force=false)
	{
		UDBSTATE result = UDBS_NONE;
		UDBSTATE st = UDBS_NONE;
		for(unsigned i = 0; i < m_vdb.size(); i++)
		{
			st = m_vdb[i]->State(force);
			if(st == UDBS_DB_UNLINK)
			{
				result = UDBS_DB_UNLINK;
			}
			else if(st == UDBS_DB_ANY_LINK && result != UDBS_DB_UNLINK)
			{
				result = UDBS_DB_ANY_LINK;
			}
			else if(st == UDBS_DB_LINK && result == UDBS_NONE)
			{
				result = UDBS_DB_LINK;
			}
		}
		return result;
	}

	virtual const char* GetConStr()
	{
		return "";
	}

	virtual UDBSQL* Stmt(const char* sql);

	/// helper function
	void set_db(int cur)
	{
		m_cur = cur;
	}

	DCDivision* identify_division(const char* sql, int& pos)
	{
		if(m_div.empty())return NULL;

		std::string s1(sql);
		tolowers(s1);
		for(std::vector<DCDivision>::iterator im = m_div.begin(); im != m_div.end(); im++)
		{
			size_t found = im->name.find("[m]");
			if(found != std::string::npos)
        	{
				std::string strtmp = im->name.substr(0, found);
				const char* p = strstr(s1.c_str(), strtmp.c_str());
				if (p)
				{
					char strcycle[10] = {0}; //获取账期
					if (s1.length()- (p-s1.c_str() + strtmp.length()) >= 6) //sql语句剩余长度需要大于等于6位
					{
						strncpy(strcycle,p + strtmp.length(), 6);
						if(atoi(strcycle)%2 == im->mon)  //取模满足mon的奇数或者偶数月份，相等则匹配成功
						{
							pos = p-s1.c_str();
							return &(*im);
						}
					}
				}
				
			}
			else
			{
				const char* p = strstr(s1.c_str(), im->name.c_str());
				if(p)
				{
					pos = p-s1.c_str();
					return &(*im);
				}
			}
			
		}
		return NULL;
	}

private:
	int								m_cur;
	std::vector<UDBConnection*>		m_vdb;
	std::vector<DCDivision>			m_div;
};

class UDBSQLDIV : public UDBSQL
{
public:
	UDBSQLDIV(UDBConnectionDIV*db, std::vector<UDBSQL*>& vql, int type, int ndb, int ntbl, int method)
		:m_db(db)
		,m_vql(vql)
		,m_ndb(ndb)
		,m_ntbl(ntbl)
		,m_method(method)
		,m_type(type)
		,m_cur(0)
	{
	}

	virtual ~UDBSQLDIV()
	{
		for(unsigned int i = 0; i<m_vql.size(); i++)
		{
			delete m_vql[i];
		}
	}

	virtual UDBSTATE State()
	{
		return m_vql[m_cur]->State();
	}

	virtual UDBSTMT  StmtType()
	{
		return m_vql[m_cur]->StmtType();
	}

	virtual UDBConnection* Connection()
	{
		return m_db;
	}

	virtual void Reset()
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->Reset();
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->Reset();
			}
		}
	}

	virtual void DivTable(const char* key)
	{
		if(m_type == 1)
		{
			if(key)
			{
				if(m_method == 1)
				{
					long lk = strtol(key, NULL, 10);
					if(lk < 0) lk = -lk;
					m_cur = lk % m_vql.size();
				}
				else
				{
					m_cur = string_hash(key) % (unsigned int)m_vql.size();
				}
				m_db->set_db( m_ndb == -1 ? m_cur/m_ntbl : m_ndb );
				m_vql[m_cur]->DivTable(key);
			}
			else
			{
				m_type = 2;
				m_cur = 0;
				m_db->set_db(m_ndb == -1 ? 0 : m_ndb);
			}
		}
		else
		{
			m_cur = 0;
			m_db->set_db(m_ndb);
		}
	}

	virtual void Execute()
	{
		if(m_type == 0)
		{
			m_db->set_db(m_ndb);
			m_vql[m_cur]->Execute();
		}
		else if(m_type == 1)
		{
			m_vql[m_cur]->Execute();
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->Execute();
			}
		}
	}

	virtual bool Next()
	{
		if(m_type != 2)
		{
			return m_vql[m_cur]->Next();
		}

		while (m_cur < m_vql.size())
		{
			if(m_vql[m_cur]->Next())
			{
				return true;
			}
			m_cur++;
			m_db->set_db(m_ndb == -1 ? m_cur/m_ntbl : m_ndb);
		}
		m_cur = 0;
		m_db->set_db(m_ndb == -1 ? 0 : m_ndb);
		return false;
	}

	virtual void Close()
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->Close();
		}
		else
		{
			m_cur = 0;
			m_db->set_db(m_ndb == -1 ? 0 : m_ndb);
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->Close();
			}
		}
	}

	virtual int  GetRowCount()
	{
		if(m_type != 2)
		{
			return m_vql[m_cur]->GetRowCount();
		}
		else
		{
			int num = 0;
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				num += m_vql[i]->GetRowCount();
			}
			return num;
		}
	}

	virtual void SetIntParam(int pos)
	{
		for(unsigned int i = 0; i<m_vql.size(); i++)
		{
			m_vql[i]->SetIntParam(pos);
		}
	}

	virtual void SetLongParam(int pos)
	{
		for(unsigned int i = 0; i<m_vql.size(); i++)
		{
			m_vql[i]->SetLongParam(pos);
		}
	}

	virtual void SetCharParam(int pos)
	{
		for(unsigned int i = 0; i<m_vql.size(); i++)
		{
			m_vql[i]->SetCharParam(pos);
		}
	}

	virtual void SetBlobParam(int pos)
	{
		for(unsigned int i = 0; i<m_vql.size(); i++)
		{
			m_vql[i]->SetBlobParam(pos);
		}
	}

	virtual void SetBindParam(const std::string& para)
	{
		for(unsigned int i = 0; i<m_vql.size(); i++)
		{
			m_vql[i]->SetBindParam(para);
		}
	}

	virtual void GetBindParam(std::string& para)
	{
		m_vql[m_cur]->GetBindParam(para);
	}

	virtual void UnBindParam()
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->UnBindParam();
		}
		else
		{
			m_cur = 0;
			m_db->set_db(m_ndb == -1 ? 0 : m_ndb);
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->UnBindParam();
			}
		}
	}

	virtual void BindParam(int pos, int value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindParam(pos, value);
			}
		}
	}

	virtual void BindParam(int pos, long value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindParam(pos, value);
			}
		}
	}

	virtual void BindParam(int pos, long long value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindParam(pos, value);
			}
		}
	}

	virtual void BindParam(int pos, const char* value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindParam(pos, value);
			}
		}
	}

	virtual void BindParam(int pos, const std::string& value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindParam(pos, value);
			}
		}
	}

	virtual void BindBlobParam(int pos, const char* value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindBlobParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindBlobParam(pos, value);
			}
		}
	}

	virtual void BindBlobParam(int pos, const std::string& value)
	{
		if(m_type != 2)
		{
			m_vql[m_cur]->BindBlobParam(pos, value);
		}
		else
		{
			for(unsigned int i = 0; i < m_vql.size(); i++)
			{
				m_vql[i]->BindBlobParam(pos, value);
			}
		}
	}

	virtual const char* GetValue(int pos)
	{
		return m_vql[m_cur]->GetValue(pos);
	}

	virtual void GetValue(int pos, char* p)
	{
		m_vql[m_cur]->GetValue(pos, p);
	}

	virtual void GetValue(int pos, std::string& value)
	{
		m_vql[m_cur]->GetValue(pos, value);
	}

	virtual void GetValue(int pos, int& value)
	{
		m_vql[m_cur]->GetValue(pos, value);
	}

	virtual void GetValue(int pos, long& value)
	{
		m_vql[m_cur]->GetValue(pos, value);
	}

	virtual void GetValue(int pos, long long& value)
	{
		m_vql[m_cur]->GetValue(pos, value);
	}

	virtual void GetSqlString(std::string& sql)
	{
		m_vql[m_cur]->GetSqlString(sql);
	}

	virtual const char* GetSql()
	{
		return m_vql[m_cur]->GetSql();
	}

	std::vector<UDBSQL*>& vSql()
	{
		return m_vql;
	}

private:
	UDBConnectionDIV*		m_db;
	std::vector<UDBSQL*>	m_vql;
	int						m_ndb;	 // -1:all, 0<=n<vdb.size
	int                     m_ntbl;	 // n>=0
	int						m_method;// 0:hash 1:mod
	int						m_type;  // 0:none 1:division 2:all
	int						m_cur;	 // current sql index
};

UDBSQL* UDBConnectionDIV::Stmt(const char* sql)
{
	UDBSQL* usql = NULL;
	UDBSQL* nsql = NULL;
	int pos = 0;
	int type = 0;
	int ndb = 0;
	int ntbl = 0;
	int method = 0;
	DCDivision* pdv = NULL;
	std::vector<UDBSQL*> vsql;
	std::string s1(sql);
	std::size_t p1 = s1.find("[P]");
	while(p1 != std::string::npos)
	{
		s1.erase(p1, 3);
		p1 = s1.find("[P]", p1);
	} 

	pdv = identify_division(sql, pos);
	if(!pdv)
	{
		// don't need division
		ndb = 0;
		ntbl = 0;
		method = 0;
		usql = m_vdb[0]->Stmt(s1.c_str());
		if(!usql)
		{
			return NULL;
		}
		vsql.push_back(usql);
	}
	else if(pdv->sdb != -1 && pdv->ntbl == 0)
	{
		// only partition
		ndb = pdv->sdb;
		ntbl = 0;
		method = pdv->method;
		usql = m_vdb[pdv->sdb]->Stmt(s1.c_str());
		if(!usql)
		{
			return NULL;
		}
		vsql.push_back(usql);
	}
	else if(pdv->sdb == -1 && pdv->ntbl == 0)
	{
		// only db division
		type = 1;
		ndb = -1;
		ntbl = 1;
		method = pdv->method;
		for(int i = 0; i < (int)m_vdb.size(); i++)
		{
			try
			{
				nsql = m_vdb[i]->Stmt(s1.c_str());
				if(nsql)
				{
					vsql.push_back(nsql);
					DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "alloc stmt success, db[%s],sql[%s]",m_vdb[i]->name(), s1.c_str());
				}
			}
			catch(UDBException& e)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, e.GetErrorCode(), "exp:[%s], db[%s], sql[%s]", e.what(), m_vdb[i]->name(), s1.c_str());
				nsql = NULL;
			}
			if(!nsql) break;
		}
		if(!nsql)
		{
			for(unsigned int k = 0; k<vsql.size(); k++)
			{
				delete vsql[k];
			}
			return NULL;
		}
	}
	else	// pdv->ntbl > 0
	{
		// db and table division
		bool bOK = true;
		type = 1;
		ndb = pdv->sdb;
		ntbl = pdv->ntbl;
		method = pdv->method;

		char snum[4]={0};
		int P[20]={0};
		int Pn = 0;
		std::string ss(sql);
		pos+=pdv->name.size();
		ss.insert(pos, "_00");

		p1 = ss.find("[P]");
		while(p1 != std::string::npos)
		{
			P[Pn] = (int)p1;
			Pn++;
			p1 = ss.find("[P]", p1+3);
		}

		for(int i = 0; i < (int)m_vdb.size(); i++)
		{
			if(ndb != -1 && i != ndb)
			{
				continue;
			}
			for(int j = 0; j < ntbl; j++)
			{
				sprintf(snum, "_%02d", j+1);
				ss.replace(pos, 3, snum);

				for(int k = 0; k < Pn; k++)
				{
					ss.replace(P[k], 3, snum);
				}

				try
				{
					nsql = m_vdb[i]->Stmt(ss.c_str());
					if(nsql)
					{
						vsql.push_back(nsql);
						DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "alloc stmt success, db[%s],sql[%s]",m_vdb[i]->name(), ss.c_str());
					}
				}catch(UDBException& e)
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, e.GetErrorCode(), "exp:[%s], db[%s], sql[%s]", e.what(), m_vdb[i]->name(), ss.c_str());
					nsql = NULL;
				}
				if(!nsql)break;
			}
			if(!nsql)break;
		}
		if(!nsql)
		{
			for(unsigned int k = 0; k<vsql.size(); k++)
			{
				delete vsql[k];
			}
			return NULL;
		}
	}
	usql = new UDBSQLDIV(this, vsql, type, ndb, ntbl, method);
	return usql;
}

std::vector<UDBSQL*> UDBSQLHK::vSql()
{
	if(m_db->isDiv())
	{
		return static_cast<UDBSQLDIV*>(m_sql)->vSql();
	}
	else
	{
		std::vector<UDBSQL*> v(1, m_sql);
		return v;
	}
}

/*----------------------------------------DCDBManerIMP------------------------------------------------*/

class DCDBManerIMP
{
	struct DCDBImp
	{
		char caty[64];
		char version[12];
		void* dlhandle;
		FUNCDBCREATE create;
		FUNCMODINIT  minit;
		std::string  args;
	};

	struct DCDBDesc
	{
		char name[64];
		char caty[64];
		char sdb[64];
		std::string env;
		std::string mdsn;
		std::string sdsn;
	};

	struct DCModDesc
	{
		char name[64];
		char db[64];
		char policy[16];
	};

	struct DCModSQL
	{
		std::string name;
		std::string mod;
		std::string sub;
		std::string param;
		std::string sql;
		std::string dyn;
	};

	struct DCDivDesc
	{
		std::string name;
		std::string sdb;
		std::string sub;
		int			ntbl;
		int			method;
		int			mon;
	};

	struct DCModRepair
	{
		char name[64];
		char db[64];
		char db2[64];
	};

	struct DCColumn
	{
		std::string name;
		char		type;
		std::string	val;
	};

	struct DCTableRepair
	{
		std::string name;
		std::string mod;
		std::string sub;
		std::string key;
		std::string dkey;
		std::string dif;
		std::string cond;
		char		bind;
		std::vector<DCColumn> column;
	};

	struct DCRepairDT
	{
		int oDB;
		int oTB;
		int nDB;
		int nTB;
	};

public:
	DCDBManerIMP()
		:m_mode(DBME_MASTER)
		,m_stat("DB")
	{}

	~DCDBManerIMP()
	{
		for(std::map<std::string, UDBSQL*>::iterator it = m_mSQL.begin(); it != m_mSQL.end(); it++)
		{
			delete it->second;
		}
		m_mSQL.clear();

		for(std::map<std::string, UDBConnection*>::iterator ic = m_mDB.begin(); ic != m_mDB.end(); ic++)
		{
			delete ic->second;
		}
		m_mDB.clear();

		for(std::vector<DCDBImp>::iterator imp = m_vDBImp.begin(); imp != m_vDBImp.end(); imp++)
		{
			dlclose(imp->dlhandle);
		}
		m_vDBImp.clear();
	}

	int Init(const char* sqlf, bool prepared = true);

	void SetDBMode(DCDBMODE mode)
	{
		m_mode = mode;
	}

	int CheckReset(bool normal=true);

	UDBConnection* GetConnection(const char* dbname)
	{
		std::map<std::string, UDBConnection*>::iterator it = m_mDB.find(dbname);
		if(it != m_mDB.end())
		{
			return it->second;
		}
		UDBConnection* con = create_conn(dbname);
		if(con) m_mDB[dbname] = con;
		return con;
	}

	UDBConnection* GetModConnection(const char* mod)
	{
		DCModDesc* mdesc = getModDesc(mod);
		if(!mdesc)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find mod desc for mod[%s]", mod);
			return NULL;
		}
		return GetConnection(mdesc->db);
	}

	UDBSQL* GetSQL(const char* name)
	{
		std::map<std::string, UDBSQL*>::iterator it = m_mSQL.find(name);
		if(it != m_mSQL.end())
		{
			return it->second;
		}
		UDBSQL* sql = create_demand_sql(name);
		if(sql) m_mSQL[name] = sql;
		return sql;
	}

	int  SetSQL(const char* mod, const char* name, const char* sub, const char* param, const char* sqltext);

	void DelSQL(const char* name)
	{
		std::map<std::string, UDBSQL*>::iterator it = m_mSQL.find(name);
		if(it != m_mSQL.end())
		{
			delete it->second;
			m_mSQL.erase(it);

			DCSYSLOG(DCLOG_LEVEL_INFO, 0, "delete sql handle [%s]", name);
		}
	}

	int  PreparedSQLByMod(const char* modname);

	bool GetSQLDescByName(const char* name, DCSQLDesc& desc);

	bool GetSQLDescByMod(const char* name, std::vector<DCSQLDesc>& vdesc);

	DCPerfStatistic* get_statistic()
	{
		return &m_stat;
	}

	int DivRepair(const char* mod = "ALL")
	{
		for(std::vector<DCTableRepair>::iterator it = m_vTableRr.begin(); it != m_vTableRr.end(); it++)
		{
			if(!strcmp(mod,"ALL") || !strcmp(it->mod.c_str(),mod))
			{
				do_table_repair(*it);
			}
		}
		return 0;
	}
	int GetAllConnStr(multimap<int,string> &mulConnStr);

private:

	int load_dbimp(TiXmlElement* elem);

	int load_dbinst(TiXmlElement* elem);

	int load_dbinst_sdb(TiXmlElement* elem, DCDBDesc& desc, bool sdb);

	int load_division(const char*name, TiXmlElement* elem);

	int load_sqldesc(TiXmlElement* elem, const char* mod);

	int load_repair(TiXmlElement* elem, const char* mod);

	int do_table_repair(DCTableRepair& table);

	int judge_table_repair(DCModRepair* modRr, const std::string& tbl, DCRepairDT& rDT);

	int do_table_real_repair(DCModRepair* modRr, DCTableRepair& tableRr, DCRepairDT& rDT, std::string& tbl,
		std::string& KField, std::string& Field, std::string& Value, std::string& Param,
		std::map<int, std::string>& Func);

	int do_table_repair_data(DCTableRepair& tableRr, DCRepairDT& rDT, std::string& tbl, std::map<int, std::string>& Func, char ktype,
		UDBSQL*QueryKey, UDBSQL*DeleteKey, UDBSQL*QueryCnt, UDBSQL*InsertCnt);

	int createDBSQL();

	int initImpArgs();

	UDBConnection* create_conn(const char* db);

	UDBConnection* create_db_conn(DCDBImp* imp, DCDBDesc* desc);

	UDBSQL* create_demand_sql(const char* name);

	UDBSQL* create_sql(const char* dbname, const char* name, const char* sqltext, const char* param);

	void split_string(const std::string& str, char sep, std::vector<std::string>& vec);

	void replace_string(std::string& sql, const std::string& bef, const std::string& aft);

	int parse_func(const std::string& text, std::string& func);

	std::string run_func(const std::string& func, const std::string& Cnt);


private:

	DCDBImp*  getDBImp(const char* caty)
	{
		std::vector<DCDBImp>::iterator it;
		for(it = m_vDBImp.begin(); it!=m_vDBImp.end(); it++)
		{
			if(!strcmp(it->caty,caty))
			{
				return &(*it);
			}
		}
		return NULL;
	}

	DCDBDesc* getDBDesc(const char* db)
	{
		std::vector<DCDBDesc>::iterator it;
		for(it = m_vDBDesc.begin(); it!=m_vDBDesc.end(); it++)
		{
			if(!strcmp(it->name,db))
			{
				return &(*it);
			}
		}
		return NULL;
	}

	DCModDesc* getModDesc(const char* name)
	{
		std::vector<DCModDesc>::iterator it;
		for(it = m_vModDesc.begin(); it!=m_vModDesc.end(); it++)
		{
			if(!strcmp(it->name,name))
			{
				return &(*it);
			}
		}
		return NULL;
	}

	DCModRepair* getModRepair(const char* name)
	{
		std::vector<DCModRepair>::iterator it;
		for(it = m_vModRr.begin(); it!=m_vModRr.end(); it++)
		{
			if(!strcmp(it->name,name))
			{
				return &(*it);
			}
		}
		return NULL;
	}

	DCModSQL* getModSQLDesc(const char* name)
	{
		std::map<std::string, int>::iterator it = m_mIndex.find(name);
		if(it != m_mIndex.end())
		{
			return &m_vDesc[it->second];
		}
		return NULL;
	}

	DCDivDesc* getDivDesc(const char* dbinst, const char* tbl)
	{
		std::multimap<std::string, DCDivDesc>::iterator it = m_mDivDesc.lower_bound(dbinst);
		std::multimap<std::string, DCDivDesc>::iterator id = m_mDivDesc.upper_bound(dbinst);
		std::vector<std::string> vsub;
		std::string vname;
		for(; it != id; it++)
		{
			if(!strcmp(it->second.name.c_str(), tbl))
			{
				return &it->second;
			}
			else if(!it->second.sub.empty())
			{
				size_t pos = it->second.name.find("[@]");
				if(pos != std::string::npos)
				{
					if(!strncmp(it->second.name.c_str(), tbl, pos))
					{
						split_string(it->second.sub, ',', vsub);
						for(unsigned int i = 0; i < vsub.size(); i++)
						{
							vname = it->second.name;
							vname.replace(pos, 3, vsub[i]);
							if(!strcmp(vname.c_str(), tbl))
							{
								return &it->second;
							}
						}
					}
				}
			}
		}
		return NULL;
	}

	void getDivDesc(const char* dbinst, std::vector<DCDivDesc>& vec)
	{
		std::multimap<std::string, DCDivDesc>::iterator it = m_mDivDesc.lower_bound(dbinst);
		std::multimap<std::string, DCDivDesc>::iterator id = m_mDivDesc.upper_bound(dbinst);
		vec.clear();
		for(; it != id; it++)
		{
			vec.push_back(it->second);
		}
	}

	void getDBDesc(const char* db, std::vector<DCDBDesc*>& vec)
	{
		std::vector<DCDBDesc>::iterator it;
		bool bfind = false;
		vec.clear();
		for(it = m_vDBDesc.begin(); it!=m_vDBDesc.end(); it++)
		{
			if(!strcmp(it->name,db))
			{
				vec.push_back(&(*it));
				bfind = true;
			}
			else if(bfind)
			{
				break;
			}
		}
	}
	
	/*解析如A=123;B=basic;C=12hj形式的键值对*/
	void parse_kv(const std::string& str, std::map<std::string, std::string>& kv)
	{
	    size_t sep = 0;
	    size_t equ = 0;
	    size_t beg = 0;
	    while(1)
		{
			equ = str.find('=', beg);
			if(equ == std::string::npos)
			{
				break;
			}
			sep = str.find(';', equ+1);
	        if(sep == std::string::npos)
			{
				kv.insert(make_pair(str.substr(beg, equ-beg), str.substr(equ+1)));
				break;
			}
			else
			{
				kv.insert(make_pair(str.substr(beg, equ-beg), str.substr(equ+1, sep-equ-1)));
			}
	        beg = sep+1;
		}
	}
	
	void DesDecrpt(DCDBDesc& desc,const char *szOriString,bool isMaster,string &strConn)
	{
		bool isConnStr = false;
		string sOri = szOriString;
		string sPrefix = "";//前缀
		int ret = DCParseXml::Instance()->ReplaceProperties(sOri, sPrefix);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "", "ReplaceProperties failed, key[%s] is invalid.",sOri.c_str());
		}
		//DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","ReplaceProperties key[%s] value[%s].",sOri.c_str(),sPrefix.c_str());
		
		if(sOri == sPrefix)//没有由配置中心配置
		{
			isConnStr = true;
		}
		else
		{
			
			string sKey =  "";
			string sValue = "";
			if(isMaster)
			{
				sKey =  sPrefix +".CONNSTR";
			}
			else
			{
				sKey =  sPrefix +".SLAVE_CONNSTR";
			}
			sValue = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
			if("" == sValue)
			{
				string sConn = "";
				string sIP = "";
				string sPort = "";
				string sUsername = "";
				string sPwd = "";
				if(isMaster)
				{
					sKey = sPrefix +".IP";
					sIP = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					sKey = sPrefix +".PORT";
					sPort = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
				}
				else
				{
					sKey = sPrefix +".SLAVE_IP";
					sIP = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					sKey = sPrefix +".SLAVE_PORT";
					sPort = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
				}
				sKey = sPrefix +".USERNAME";
				sUsername = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
				
				//其中密码需要解密
				sKey = sPrefix +".PWD";
				sPwd = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
				char szOut[512] = {0};
				m_encrypt.strDesDecrpt(sPwd.c_str(),"TyDic@2015",szOut);
				sPwd = szOut;
				
				//根据category区分dca/dmdb/mysql获取相关配置，拼接成串
				if(strcmp(desc.caty,"UDCA")==0)
				{
					//IP=**************;PORT=17081;UUID=TEST;ACCTID=tydic;USER=test;PWD=123456
					string sUuid = "";
					string sAcctid = "";
					sKey = sPrefix +".UUID";
					sUuid = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					sKey = sPrefix +".ACCT_ID";
					sAcctid = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					strConn = "IP=" + sIP + ";PORT=" + sPort + ";UUID=" + sUuid + ";ACCTID=" + sAcctid + ";USER=" + sUsername + ";PWD=" + sPwd;
				}
				else if(strcmp(desc.caty,"UDBDRIVER")==0)
				{
					int nMysqlType = 0;		//0:DMDB 1:MYSQL UDAL 2:MYSQL single
					int nDBType = 0;
					std::map<std::string, std::string> kv;

					if(!desc.env.empty())
					{
						parse_kv(desc.env, kv);
						nDBType = atoi(kv["DBTYPE"].c_str());	       
						
						if(kv.count("DBJT"))
						{
							nMysqlType = atoi(kv["DBJT"].c_str());
						}
						else if(kv.count("CONTYPE"))
						{
							nMysqlType = atoi(kv["CONTYPE"].c_str());
						}	
					}
					string sDsnname = "";
					sKey = sPrefix +".DSN_NAME";
					sDsnname = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);					
					if(nDBType == 0)
					{
						//DSN=ahdmdbtt
						strConn = "DSN=" + sDsnname;
					}
					else if(nDBType == 3)
					{
						//xzbill_rat/xzbill_rat_2020@ORA94
						strConn = sUsername + "/" + sPwd + "@" + sDsnname;
					}
					else if(nDBType == 6)
					{
						if (0 == nMysqlType)  //DMDB(libmydrv.so驱动)
						{
							//odbc:mysql//**************:6088/dmdb?user=dmdb&amp;password=********************* "odbc:mysql//" + sIP + ":" + sPort + "/" + sDsnname + "?user=" + sUsername + "&password=" + sPwd;
						}
						else
						{
							//odbc:mysql//**************:8901/DUCC1_BILL_JX?user=dca&amp;password=************************** "odbc:mysql//" + sIP + ":" + sPort + "/" + sDsnname + "?user=" + sUsername + "&password=" + sPwd;
						}				
					}
					else if(nDBType == 7)  //DMDB(libtdaldrv.so驱动)
					{
						//odbc:mysql//**************:6088/dmdb?user=dmdb&amp;password=******************** "odbc:mysql//" + sIP + ":" + sPort + "/" + sDsnname + "?user=" + sUsername + "&password=" + sPwd;
					}					
					else
					{
						strConn = szOriString;
					}
				}
				else if(strcmp(desc.caty,"UPOSTGRE")==0)
				{
					string sDBname = "";
					sKey = sPrefix +".DB_NAME";
					sDBname = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					//user=lisanyi password=lisanyi hostaddr=************** port=5432 dbname=ah_mon
					strConn = "user=" + sUsername + " password=" + sPwd + " hostaddr=" + sIP + " port=" + sPort + " dbname=" + sDBname;
				}
				else if(strcmp(desc.caty,"URMDB")==0)
				{
					string sDsnname = "";
					sKey = sPrefix +".DSN_NAME";
					sDsnname = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					
					//xzbill_rat/xzbill_rat_2020@ORA94
					strConn = sUsername + "/" + sPwd + "@" + sDsnname;
				}
				else if(strcmp(desc.caty,"UHBASE")==0)
				{
					string sFetch = "";
					sKey = sPrefix +".FETCH";
					sFetch = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					string sTimeout = "";
					sKey = sPrefix +".TIMEOUT";
					sTimeout = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					string sAutoCommit = "";
					sKey = sPrefix +".AUTOCOMMIT";
					sAutoCommit = DCParseXml::Instance()->GetParam(sKey.c_str(),NULL);
					
					//IP=***************;PORT=10086;FETCH=12000;TIMEOUT=10;AUTOCOMMIT=0
					strConn = "IP=" + sIP + ";PORT=" + sPort +";FETCH=" + sFetch + ";TIMEOUT=" + sTimeout + ";AUTOCOMMIT" + sAutoCommit;
				}
				else if(strcmp(desc.caty,"UM2DB")==0)
				{
					strConn = sPrefix;
				}
				else
				{
					strConn = szOriString;
				}
			}
			else
			{
				isConnStr = true;
				if(!sValue.empty())
					sOri = sValue;
			}
		}

		if (isConnStr)
		{
			char *p = (char *)szOriString;
			char *pstr1 = strstr(p, "=");
			char *pstr2 = strstr(p, "@");
			char *pstr3 = strstr(p, ":");
			if (strlen(szOriString) > 5 && !pstr1 && !pstr2 && !pstr3)
			{
				char szOut[512] = {0};
				m_encrypt.strDesDecrpt(szOriString, "TyDic@2015", szOut);
				strConn.clear();
				strConn = szOut;
				int pos = strConn.find("&amp;");
				if (pos > 0)
				{
					strConn.replace(pos, 5, "&");
				}
				// DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "DesDecrpt strConn[%s]", strConn.c_str());
				
			}
		}
		DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "DesDecrpt strConn[***]" /*, strConn.c_str()*/);
	}

private:
	DCDBMODE								m_mode;
	std::vector<DCDBImp>					m_vDBImp;
	std::vector<DCDBDesc>					m_vDBDesc;
	std::vector<DCModDesc>					m_vModDesc;
	std::vector<DCModSQL>					m_vDesc;
	std::vector<DCModRepair>				m_vModRr;
	std::vector<DCTableRepair>				m_vTableRr;
	std::multimap<std::string, DCDivDesc>	m_mDivDesc;
	std::map<std::string, int>				m_mIndex;
	std::multimap<std::string, std::string>	m_mExtd;

	std::map<std::string, UDBConnection*> 	m_mDB;
	std::map<std::string, UDBSQL*>			m_mSQL;
	DCPerfStatistic								m_stat;

	static bool								m_minited;
	static bool								m_mInitXml;
	CEncryptCpt                             m_encrypt;

};

bool DCDBManerIMP::m_minited = false;
bool DCDBManerIMP::m_mInitXml = false;

int DCDBManerIMP::Init(const char* sqlf, bool prepared)
{
	int ret = 0;

	if(!m_mInitXml)
	{
		m_mInitXml = true;
		ret = DCParseXml::Instance()->Init(NULL,NULL);
		if(ret)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "InitProperties failed");
			return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "InitProperties success");
	}
	
	TiXmlDocument doc(sqlf);

	doc.LoadFile();
	if(doc.Error())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load file[%s] failed: %s", sqlf, doc.ErrorDesc());
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load file[%s] success", sqlf);

	TiXmlElement* elem = NULL;

	TiXmlElement* root_elem = doc.RootElement();
	if(!root_elem)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find root element for file[%s]", sqlf);
		return -1;
	}

	TiXmlElement* data = root_elem->FirstChildElement("data");
	if(!data)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find root/data element for file[%s]", sqlf);
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load db info");

	elem = data->FirstChildElement("dbimp");
	if(elem)
	{
		for(elem = elem->FirstChildElement("imp"); elem; elem = elem->NextSiblingElement("imp"))
		{
			ret = load_dbimp(elem);
			if(ret)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load_dbimp failed for imp[%lu]", m_vDBImp.size());
				return -1;
			}
		}
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load dbimp success, size[%lu]", m_vDBImp.size());

	elem = data->FirstChildElement("dbinst");
	if(elem)
	{
		for(elem = elem->FirstChildElement("db"); elem; elem = elem->NextSiblingElement("db"))
		{
			ret = load_dbinst(elem);
			if(ret)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load_dbinst failed for imp[%lu]", m_vDBDesc.size());
				return -1;
			}
		}
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load dbinst success, size[%lu]", m_vDBDesc.size());

	for( elem = data->FirstChildElement("division"); elem; elem = elem->NextSiblingElement("division"))
	{
		const char* name = elem->Attribute("dbinst");
		if(!name || !name[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the Attribute [dbinst] must exist in division");
			return -1;
		}

		if(!getDBDesc(name))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find db desc for dbinst[%s]", name);
			return -1;
		}

		for(TiXmlElement* el = elem->FirstChildElement("table"); el; el = el->NextSiblingElement("table"))
		{
			ret = load_division(name, el);
			if(ret)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load_division failed for dbinst[%s]", name);
				return -1;
			}
		}
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load division success, size[%lu]", m_mDivDesc.size());

	TiXmlElement* app = root_elem->FirstChildElement("app");
	if(!app)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find root/app element for file[%s]", sqlf);
		return -1;
	}

	TiXmlElement* mod = NULL;
	const char* name = NULL;
	const char* dbname = NULL;
	const char* db2name = NULL;
	const char* policy = NULL;
	DCModDesc moddesc;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load sql info");

	for(mod = app->FirstChildElement("module"); mod; mod = mod->NextSiblingElement("module"))
	{
		name = mod->Attribute("name");
		dbname = mod->Attribute("db");
		policy = mod->Attribute("policy");
		if(!name || !dbname ||!policy)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the Attribute [name] [db] [policy] of module must always exist");
			return -1;
		}

		if(strcmp(policy, "must") && strcmp(policy, "demand"))
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "skip module[%s], policy[%s]", name, policy);
			continue;
		}

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "module name[%s], db[%s], policy[%s]", name, dbname, policy);

		if(!getDBDesc(dbname))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find db desc for db[%s]", dbname);
			return -1;
		}

		strcpy(moddesc.name, name);
		strcpy(moddesc.db, dbname);
		strcpy(moddesc.policy, policy);

		m_vModDesc.push_back(moddesc);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load sql info for module[%s]", name);
		for(elem = mod->FirstChildElement("sql"); elem; elem = elem->NextSiblingElement("sql"))
		{
			ret = load_sqldesc(elem, name);
			if(ret)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load_sqldesc failed for module[%s]", name);
				return -1;
			}
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load sql info for module[%s]", name);
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load sql info success, module size[%lu], sql size[%lu]", \
		m_vModDesc.size(), m_vDesc.size());

	TiXmlElement* repair = root_elem->FirstChildElement("repair");
	if(repair)
	{
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load repair info");
		DCModRepair modRr;
		for(mod = repair->FirstChildElement("module"); mod; mod = mod->NextSiblingElement("module"))
		{
			name = mod->Attribute("name");
			dbname = mod->Attribute("db_from");
			db2name = mod->Attribute("db_to");
			policy = mod->Attribute("policy");
			if(!name || !dbname || !db2name ||!policy)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the Attribute [name] [db_from] [db_to] [policy] of module must always exist");
				return -1;
			}

			if(strcmp(policy, "must"))
			{
				DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "skip module[%s], policy[%s]", name, policy);
				continue;
			}

			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "module name[%s], db_from[%s], db_to[%s], policy[%s]", name, dbname, db2name, policy);

			if(!getDBDesc(dbname))
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find db desc for db[%s]", dbname);
				return -1;
			}

			if(!getDBDesc(db2name))
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find db desc for db[%s]", db2name);
				return -1;
			}

			strcpy(modRr.name, name);
			strcpy(modRr.db, dbname);
			strcpy(modRr.db2, db2name);

			m_vModRr.push_back(modRr);

			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to load repair table info for module[%s]", name);
			for(elem = mod->FirstChildElement("table"); elem; elem = elem->NextSiblingElement("table"))
			{
				ret = load_repair(elem, name);
				if(ret)
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "load_repair failed for module[%s]", name);
					return -1;
				}
			}
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to load repair table info for module[%s]", name);
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "load repair info success, module size[%lu], table size[%lu]", \
			m_vModRr.size(), m_vTableRr.size());
	}

	if(!m_minited)
	{
		m_minited = true;
		ret = initImpArgs();
		if(ret)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "initImpArgs failed");
			return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "initImpArgs success");
	}

	if(prepared)
	{
		ret = createDBSQL();
		if(ret)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "createDBSQL failed");
			return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "createDBSQL success");
	}
	return 0;
}

int DCDBManerIMP::initImpArgs()
{
	int ret = 0;
	for(unsigned int i = 0; i < m_vDBImp.size(); i++)
	{
		DCDBImp& imp = m_vDBImp[i];
		if(imp.minit)
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "dbimp [%s] start init args", imp.caty);
			ret = imp.minit(imp.args.c_str());
			if(ret)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "dbimp [%s] init args failed", imp.caty);
				return -1;
			}
		}
	}
	return 0;
}

int DCDBManerIMP::load_dbimp(TiXmlElement* elem)
{
	const char* attr = NULL;
	DCDBImp imp;
	attr = elem->Attribute("category");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[category] in dbimp element");
		return -1;
	}
	strcpy(imp.caty, attr);

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find dbimp [%s]", imp.caty);

	attr = elem->Attribute("version");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[version] for dbimp[%s]", imp.caty);
		return -1;
	}
	strcpy(imp.version, attr);

	attr = elem->Attribute("args");
	if(attr && attr[0])
	{
		imp.args = attr;
	}

	attr = elem->GetText();
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find path for dbimp[%s]", imp.caty);
		return -1;
	}

	std::string spath = attr;
	int pos = spath.rfind('/');
	if(std::string::npos==pos)
	{
		char *szenv=getenv("OCS_HOME");
		char buf[256]={0};
		strncpy(buf,szenv,sizeof(buf)-1);
		strcat(buf,"/lib/");
		strcat(buf,attr);

		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "find dbimp category[%s] version[%s] path[%s] ", imp.caty, imp.version, buf);

		dlerror();
		imp.dlhandle = dlopen(buf, RTLD_LAZY);
		if(!imp.dlhandle)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "dlopen file[%s] failed: [%s]", buf, dlerror());
			return -1;
		}
	}
	else
	{

		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "find dbimp category[%s] version[%s] args[%s] path[%s] ", imp.caty, imp.version, imp.args.c_str(), attr);

		dlerror();
		imp.dlhandle = dlopen(attr, RTLD_LAZY);
		if(!imp.dlhandle)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "dlopen file[%s] failed: [%s]", attr, dlerror());
			return -1;
		}
	}

	imp.create = (FUNCDBCREATE)dlsym(imp.dlhandle, "s_dbconn_create");
	if(!imp.create)
	{
		dlclose(imp.dlhandle);
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find symbol[s_dbconn_create]");
		return -1;
	}

	imp.minit = (FUNCMODINIT)dlsym(imp.dlhandle, "s_mod_init_args");

	const char* category = *(const char**)dlsym(imp.dlhandle, "mod_category");
	const char* version = *(const char**)dlsym(imp.dlhandle, "mod_version");

	if(!category || !version)
	{
		dlclose(imp.dlhandle);
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the symbol [mod_category] [mod_version] must always exist");
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "dbimp symbol category[%s], version[%s], dlhandle[%p]", \
		category, version, imp.dlhandle);

	if(strcmp(imp.caty, category))
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the dbimp category[%s] != mod_category[%s]", imp.caty, category);
		dlclose(imp.dlhandle);
		return -1;
	}

	if(strcmp(imp.version, version))
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the dbimp version[%s] != mod_version[%s]", imp.version, version);
		dlclose(imp.dlhandle);
		return -1;
	}
	m_vDBImp.push_back(imp);
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "dbimp[%s] check ok!", imp.caty);
	return 0;
}

int DCDBManerIMP::load_dbinst(TiXmlElement* elem)
{
	const char* attr = NULL;
	DCDBDesc desc;
	desc.sdb[0] = 0x0;

	attr = elem->Attribute("name");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] in dbinst element");
		return -1;
	}
	strcpy(desc.name, attr);

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find dbinst[%s]", desc.name);

	attr = elem->Attribute("category");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[category] for dbinst[%s]", desc.name);
		return -1;
	}
	strcpy(desc.caty, attr);

	if(!getDBImp(desc.caty))
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find dbimp for category[%s]", desc.caty);
		return -1;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find dbimp for category[%s]", desc.caty);

	TiXmlElement* env = elem->FirstChildElement("env");
	if(env && env->GetText())
	{
		desc.env = env->GetText();
	}

	TiXmlElement* sdb = elem->FirstChildElement("sdb");
	if(sdb)
	{
		for(; sdb; sdb = sdb->NextSiblingElement("sdb"))
		{
			desc.sdb[0] = 0x0;
			load_dbinst_sdb(sdb, desc, true);
		}
	}
	else
	{
		load_dbinst_sdb(elem, desc, false);
	}
	return 0;
}

int DCDBManerIMP::load_dbinst_sdb(TiXmlElement* elem, DCDBDesc& desc, bool sdb)
{
	if(sdb)
	{
		const char* attr = elem->Attribute("name");
		if(attr)
		{
			strcpy(desc.sdb, attr);
		}
	}

	TiXmlElement* dsn = elem->FirstChildElement("master");
	if(dsn && dsn->GetText())
	{
		desc.mdsn = dsn->GetText();
		DesDecrpt(desc,dsn->GetText(),true,desc.mdsn);

	}
	else
	{
		desc.mdsn.clear();
	}

	dsn = elem->FirstChildElement("standby");
	if(dsn && dsn->GetText())
	{
		desc.sdsn = dsn->GetText();
		DesDecrpt(desc,dsn->GetText(),false,desc.sdsn);

	}
	else
	{
		desc.sdsn.clear();
	}

	if(desc.mdsn.empty())
	{
		desc.mdsn = desc.sdsn;
		desc.sdsn.clear();
	}
	m_vDBDesc.push_back(desc);

	//DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "dbinst name[%s] category[%s] env[%s] sdb[%s] master[%s] standby[%s]", \
	//	desc.name, desc.caty, desc.env.c_str(), desc.sdb, desc.mdsn.c_str(), desc.sdsn.c_str());
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "dbinst name[%s] category[%s] env[%s] sdb[***] master[***] standby[%s]", \
		desc.name, desc.caty, desc.env.c_str(), /*desc.sdb, desc.mdsn.c_str(), */desc.sdsn.c_str());
	return 0;
}

int DCDBManerIMP::load_division(const char*name, TiXmlElement* elem)
{
	const char* attr = NULL;
	DCDivDesc desc;
	std::vector<DCDBDesc*> vSDB;

	getDBDesc(name, vSDB);

	attr = elem->Attribute("name");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the Attribute [name] must exist in division/table");
		return -1;
	}

	desc.name = attr;

	attr = elem->Attribute("sdb");
	if(!attr || !attr[0] || !strcmp(attr, "*"))
	{
		attr = "*";
	}
	else
	{
		// check sdb exists
		unsigned int i = 0;
		for(; i < vSDB.size(); i++)
		{
			if(!strcmp(vSDB[i]->sdb, attr))
			{
				break;
			}
		}
		if(i == vSDB.size())
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the sdb[%s] in division/table is not available");
			return -1;
		}
	}
	desc.sdb = attr;

	attr = elem->Attribute("ntbl");
	if(!attr || !attr[0])
	{
		desc.ntbl = 0;
	}
	else
	{
		desc.ntbl = atoi(attr);
	}

	if(desc.ntbl < 0 || desc.ntbl > 99)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "table[%s] [ntbl=%d] must be range[0-99] in division/table", desc.name.c_str(),desc.ntbl);
		return -1;
	}

	if(desc.name.find("[@]") != std::string::npos)
	{
		attr = elem->Attribute("sub");
		if(!attr || !attr[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the Attribute [sub] must exist for name[%s] in division/table", desc.name.c_str());
			return -1;
		}
		desc.sub = attr;
	}

	attr = elem->Attribute("method");
	if(!attr || !attr[0])
	{
		desc.method = 0;
	}
	else
	{
		if(!strcmp(attr, "hash"))
		{
			desc.method = 0;
		}
		else if(!strcmp(attr, "mod"))
		{
			desc.method = 1;
		}
		else
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the Attribute [method] must be 'hash' or 'mod' for name[%s] in division/table", desc.name.c_str());
			return -1;
		}
	}

	attr = elem->Attribute("mon");
	if(!attr || !attr[0])
	{
		desc.mon = -1;
	}
	else
	{
		desc.mon = atoi(attr);

		if(desc.mon != 0 && desc.mon != 1)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "table[%s] [mon=%d] must be range[0-1] in division/table", desc.name.c_str(),desc.mon);
			return -1;
		}
	}
	
	m_mDivDesc.insert(std::pair<std::string, DCDivDesc>(name, desc));

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find division table[%s] sdb[%s] ntbl[%d] method[%d] sub[%s] mon[%d] for dbinst[%s]", \
		desc.name.c_str(),desc.sdb.c_str(), desc.ntbl,desc.method,desc.sub.c_str(),desc.mon,name);
	return 0;
}

int DCDBManerIMP::load_sqldesc(TiXmlElement* elem, const char* mod)
{
	const char* attr = NULL;
	DCModSQL desc;

	desc.mod = mod;

	attr = elem->Attribute("name");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] in sql element");
		return -1;
	}
	desc.name = attr;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find sql[%s]", desc.name.c_str());

	if(m_mIndex.count(desc.name))
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "already exist sql[%s]", desc.name.c_str());
		return -1;
	}

	attr = elem->Attribute("bind");
	if(attr)
	{
		desc.param = attr;
	}

	attr = elem->Attribute("sub");
	if(attr)
	{
		//sub需要做替换 ReplaceProperties 
		string sori = attr;
		int ret = DCParseXml::Instance()->ReplaceProperties(sori,desc.sub);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "", "ReplaceProperties failed, key[%s] is invalid.",sori.c_str());
		}
		//DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","ReplaceProperties key[%s] value[%s].",sori.c_str(),desc.sub.c_str());
	}

	attr = elem->Attribute("dyn");
	if(attr)
	{
		desc.dyn = attr;
		for(int i =0; attr[i]; i++)
		{
			if(attr[i] <'A' || attr[i] >'C')
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid attr dyn[%s] in sql[%s]", attr,desc.name.c_str());
				return -1;
			}
		}
		if(desc.dyn.size()>3)
		{
			desc.dyn.resize(3);
		}
	}

	attr = elem->GetText();
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find sqltext for sql[%s]", desc.name.c_str());
		return -1;
	}
	//sql需要做替换 ReplaceProperties
	string sori = attr;
	int ret = DCParseXml::Instance()->ReplaceProperties(sori,desc.sql);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "", "ReplaceProperties failed, key[%s] is invalid.",sori.c_str());
	}
	//DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","ReplaceProperties key[%s] value[%s].",sori.c_str(),desc.sql.c_str());
	
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "sql: mod[%s] name[%s] bind[%s] sub[%s] dyn[%s] text[%s]", \
		desc.mod.c_str(), desc.name.c_str(), desc.param.c_str(), desc.sub.c_str(), desc.dyn.c_str(), desc.sql.c_str());

	m_vDesc.push_back(desc);

	int idx = (int)m_vDesc.size()-1;
	m_mIndex[desc.name] = idx;

	DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "sql index: name[%s], idx[%d]", desc.name.c_str(), idx);

	if(!desc.sub.empty())
	{
		std::string name;
		std::vector<std::string> vsub;
		split_string(desc.sub, ',', vsub);
		for(unsigned int i=0; i< vsub.size(); i++)
		{
			name = desc.name+std::string("|")+vsub[i];
			m_mIndex[name] = idx;

			DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "sql index: name[%s], idx[%d]", name.c_str(), idx);
		}
	}

	return 0;
}

int DCDBManerIMP::load_repair(TiXmlElement* elem, const char* mod)
{
	TiXmlElement* item = NULL;
	const char* attr = NULL;
	DCTableRepair tableRr;
	DCColumn col;

	tableRr.mod = mod;

	attr = elem->Attribute("name");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] in table element");
		return -1;
	}
	tableRr.name = attr;

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find table[%s]", tableRr.name.c_str());

	attr = elem->Attribute("sub");
	if(attr)
	{
		tableRr.sub = attr;
	}

	attr = elem->Attribute("key");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[key] in table[%s] element", tableRr.name.c_str());
		return -1;
	}
	tableRr.key = attr;

	item = elem->FirstChildElement("cond");
	if(item && item->GetText())
	{
		tableRr.cond = item->GetText();
	}

	item = elem->FirstChildElement("dif");
	if(item)
	{
		attr = item->Attribute("dkey");
		if(!attr || !strstr(attr, "[K]"))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid Attribute[dkey=%s] in table[%s]/dif element", attr,tableRr.name.c_str());
			return -1;
		}
		tableRr.dkey = attr;

		attr = item->GetText();
		if(!attr || !attr[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "empty value in table[%s]/dif element", tableRr.name.c_str());
			return -1;
		}

		if(strncmp(attr, "fn_", 3))
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid value[%s] in table[%s]/dif element", attr, tableRr.name.c_str());
			return -1;
		}
		tableRr.dif = attr;
	}

	item = elem->FirstChildElement("column");
	if(!item)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find element[column] in table[%s] element", tableRr.name.c_str());
		return -1;
	}

	attr = item->Attribute("bind_value");
	if(!attr || !attr[0])
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[bind_value] in table[%s]/column element", tableRr.name.c_str());
		return -1;
	}

	if(attr[0] != '?' && attr[0] != ':')
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid Attribute[bind_value=%s] in table[%s]/column element", attr,tableRr.name.c_str());
		return -1;
	}
	tableRr.bind = attr[0];

	for(item = item->FirstChildElement("col"); item; item = item->NextSiblingElement("col"))
	{
		attr = item->Attribute("name");
		if(!attr || !attr[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[name] in table/column/col element");
			return -1;
		}
		col.name = attr;

		attr = item->Attribute("type");
		if(!attr || !attr[0])
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find Attribute[type] in table/column/col element");
			return -1;
		}
		col.type = attr[0];
		if(col.type < '1' || col.type > '4')
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid Attribute[type=%s] in table/column/col element", attr);
			return -1;
		}

		attr = item->Attribute("value");
		col.val = attr? attr : "";

		tableRr.column.push_back(col);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "col: table[%s] name[%s] type[%c] value[%s]", \
			tableRr.name.c_str(), col.name.c_str(), col.type, col.val.c_str());
	}

	if(tableRr.column.empty())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "table/column/col element is empty, skip table[%s]", tableRr.name.c_str());
		return 0;
	}

	m_vTableRr.push_back(tableRr);

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "table: mod[%s] name[%s] sub[%s] key[%s] cond[%s] bind[%c] colsz[%lu]", \
		tableRr.mod.c_str(), tableRr.name.c_str(), tableRr.sub.c_str(), tableRr.key.c_str(), tableRr.cond.c_str(), tableRr.bind, tableRr.column.size());
	return 0;
}

// output: func,param1,param2
int DCDBManerIMP::parse_func(const std::string& text, std::string& func)
{
	func.clear();
	if(strncmp(text.c_str(), "fn_", 3))
	{
		return -1;
	}
	size_t b = text.find('(');
	size_t d = text.find(')');
	if(b == std::string::npos || d == std::string::npos)
	{
		return -1;
	}
	std::vector<std::string> vpara;
	split_string(text.substr(b+1, d-b-1), ',', vpara);

	std::string tmp = text.substr(0, b);
	trim(tmp);
	size_t sep = 0;
	if(!strcmp(tmp.c_str(),"fn_eq"))
	{
		if(vpara.size() != 2)
		{
			return -1;
		}
		// fn_name
		func = tmp;
		for(unsigned int i = 0; i < vpara.size(); i++)
		{
			func+= std::string(",")+ trim(vpara[i]);
		}
		return 0;
	}
	else if(!strcmp(tmp.c_str(),"fn_skip"))
	{
		if(vpara.size() != 4)
		{
			return -1;
		}
		// fn_name
		func = tmp;
		for(unsigned int i = 0; i < vpara.size(); i++)
		{
			func+= std::string(",")+ trim(vpara[i]);
		}
		return 0;
	}
	return -1;
}

std::string DCDBManerIMP::run_func(const std::string& func, const std::string& Cnt)
{
	std::vector<std::string> va;
	split_string(func, ',', va);
	if(va[0] == "fn_eq")
	{
		for(int i = 1; i < 3; i++)
		{
			if(va[i][0] == '@')
			{
				va[i] = Cnt;
			}
		}
		return va[1] == va[2] ? "true" : "false";
	}
	if(va[0] == "fn_skip")
	{
		return fn_skip(Cnt,va[2],atoi(va[3].c_str()), atoi(va[4].c_str()));
	}
	return "";
}

int DCDBManerIMP::do_table_repair(DCTableRepair& tableRr)
{
	DCSYSLOG(DCLOG_LEVEL_INFO, 0, "start to do table repair, table[%s], sub[%s]", \
		tableRr.name.c_str(), tableRr.sub.c_str());
	DCModRepair* modRr = getModRepair(tableRr.mod.c_str());

	std::vector<std::string> vsub;
	if(!tableRr.sub.empty())
	{
		split_string(tableRr.sub, ',', vsub);
	}

	std::string KField;
	std::string Field;
	std::string Value;
	std::string Param;
	std::string fn;
	std::map<int, std::string> Func;

	int ret = 0;
	char buf[8];

	KField=tableRr.key;
	if(!tableRr.dif.empty())
	{
		ret = parse_func(tableRr.dif, fn);
		if(ret < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "parse func failed for dif[%s] in table[%s]",tableRr.dif.c_str(), tableRr.name.c_str());
			return ret;
		}
		Func[-1] = fn;
		size_t pb = fn.find('@');
		size_t pd = fn.find(',',pb+1);
		if(pd == std::string::npos)
		{
			pd = fn.size();
		}
		KField.append(",").append(fn.substr(pb+1, pd-pb-1));
	}

	std::vector<DCColumn>& column = tableRr.column;
	for(unsigned int i = 0, j=0; i < column.size(); i++)
	{
		Field.append(column[i].name).append(",");
		if(!column[i].val.empty() && !strncmp(column[i].val.c_str(),"fn_", 3))
		{
			ret = parse_func(column[i].val, fn);
			if(ret < 0)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "parse func failed for column value[%s] in table[%s]",column[i].val.c_str(), tableRr.name.c_str());
				return ret;
			}
			Func[i] = fn;
		}

		if(!column[i].val.empty() && strncmp(column[i].val.c_str(),"fn_", 3))
		{
			// constant values
			Value.append(column[i].val).append(",");
		}
		else if(tableRr.bind == ':')
		{
			// oracle values
			j++;
			sprintf(buf, ":%d,", j);
			Value.append(buf);
			Param.append(1, column[i].type);
		}
		else
		{
			// timesten values
			Value.append("?,");
			Param.append(1, column[i].type);
		}
	}

	Field.erase(Field.size()-1);
	Value.erase(Value.size()-1);

	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s], KField[%s]", tableRr.name.c_str(), KField.c_str());
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s], Field[%s]", tableRr.name.c_str(), Field.c_str());
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s], Value[%s]", tableRr.name.c_str(), Value.c_str());
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s], Param[%s]", tableRr.name.c_str(), Param.c_str());

	DCRepairDT rDT;

	if(vsub.empty())
	{
		ret = judge_table_repair(modRr, tableRr.name, rDT);
		if(ret == 1)
		{
			DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "judge need repaired, table[%s]", tableRr.name.c_str());
			return do_table_real_repair(modRr, tableRr, rDT, tableRr.name, KField, Field, Value, Param, Func);
		}
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "judge do not need repaired, table[%s]", tableRr.name.c_str());
	}
	else
	{
		size_t pos = 0;
		std::string tblname;
		for(int j=0; j < vsub.size(); j++)
		{
			tblname = tableRr.name;
			pos = tblname.find("[@]");
			while(pos != std::string::npos)
			{
				tblname.replace(pos, 3, vsub[j]);
				pos = tblname.find("[@]", pos+vsub[j].size());
			}

			ret = judge_table_repair(modRr, tblname, rDT);
			if(ret == 1)
			{
				DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "judge need repaired, table[%s]", tblname.c_str());
				ret = do_table_real_repair(modRr, tableRr, rDT, tblname, KField, Field, Value, Param, Func);
			}
			else
			{
				DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "judge do not need repaired, table[%s]", tblname.c_str());
			}
		}
	}
	return 0;
}

int DCDBManerIMP::judge_table_repair(DCModRepair* modRr, const std::string& tbl, DCRepairDT& rDT)
{
	std::vector<DCDBDesc*> vDB1;
	std::vector<DCDBDesc*> vDB2;
	getDBDesc(modRr->db, vDB1);
	getDBDesc(modRr->db2, vDB2);

	DCDivDesc* div1 = getDivDesc(modRr->db, tbl.c_str());
	DCDivDesc* div2 = getDivDesc(modRr->db2, tbl.c_str());

	std::string db1 = "DBALL1";
	std::string db2 = "DBALL2";

	// fill the DCRepairDT
	if(div1 && strcmp(div1->sdb.c_str(), "*"))
	{
		rDT.oDB = 1;
		rDT.oTB = div1->ntbl;
	}
	else if(div1)
	{
		rDT.oDB = (int)vDB1.size();
		rDT.oTB = div1->ntbl;
	}
	else
	{
		rDT.oDB = 1;
		rDT.oTB = 0;
	}

	if(div2 && strcmp(div2->sdb.c_str(), "*"))
	{
		rDT.nDB = 1;
		rDT.nTB = div2->ntbl;
	}
	else if(div2)
	{
		rDT.nDB = (int)vDB2.size();
		rDT.nTB = div2->ntbl;
	}
	else
	{
		rDT.nDB = 1;
		rDT.nTB = 0;
	}

	// only one database, assign to db1
	if(vDB1.size() == 1 || !div1)
	{
		db1 = vDB1[0]->mdsn;
	}
	else if(strcmp(div1->sdb.c_str(), "*"))
	{
		for(unsigned int i = 0; i < vDB1.size(); i++)
		{
			if(!strcmp(vDB1[i]->sdb, div1->sdb.c_str()))
			{
				db1 = vDB1[i]->mdsn;
				break;
			}
		}
	}

	// only one database, assign to db2
	if(vDB2.size() == 1 || !div2)
	{
		db2 = vDB2[0]->mdsn;
	}
	else if(strcmp(div2->sdb.c_str(), "*"))
	{
		for(unsigned int i = 0; i < vDB2.size(); i++)
		{
			if(!strcmp(vDB2[i]->sdb, div2->sdb.c_str()))
			{
				db2 = vDB2[i]->mdsn;
				break;
			}
		}
	}

	// not a same database
	if(db1 != db2)
	{
		return 1;
	}

	// in a same database and not division
	if(!div1 && !div2)
	{
		return 0;
	}

	// in a same database, div2 limited to only one database and not division
	if(!div1 && div2 && !div2->ntbl)
	{
		return 0;
	}

	// in a same database, div1 limited to only one database and not division
	if(!div2 && div1 && !div1->ntbl)
	{
		return 0;
	}

	// in a same database, both div1/div2 limited to only one database and not division
	if(div1 && div2 && !div1->ntbl && !div2->ntbl)
	{
		return 0;
	}

	return 1;
}

int DCDBManerIMP::do_table_real_repair(DCModRepair* modRr, DCTableRepair& tableRr, DCRepairDT& rDT, std::string& tbl,
	std::string& KField, std::string& Field, std::string& Value, std::string& Param,
	std::map<int, std::string>& Func)
{
	UDBConnectionHK* db1 = static_cast<UDBConnectionHK*>(GetConnection(modRr->db));
	if(!db1)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "failed to do table repair, table[%s], GetConnection failed for dbint[%s]", tbl.c_str(), modRr->db);
		return -1;
	}
	UDBConnectionHK* db2 = static_cast<UDBConnectionHK*>(GetConnection(modRr->db2));
	if(!db2)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "failed to do table repair, table[%s], GetConnection failed for dbint[%s]", tbl.c_str(), modRr->db2);
		return -1;
	}

	std::string qryKey;
	std::string delKey;
	std::string qryCnt;
	std::string InsCnt;

	char ktype = 0;
	for(unsigned int i=0; i<tableRr.column.size(); i++)
	{
		if(!strcasecmp(tableRr.column[i].name.c_str(), tableRr.key.c_str()))
		{
			ktype = tableRr.column[i].type;
			break;
		}
	}
	if(!ktype)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "not find type for key[%s] in table[%s]", tableRr.key.c_str(),tableRr.name.c_str());
		return -1;
	}

	qryKey.append("select ").append(KField).append(" from ").append(tbl);
	if(!tableRr.cond.empty())
	{
		qryKey.append(" where ").append(tableRr.cond);
	}

	delKey.append("delete from ").append(tbl).append(" where ").append(tableRr.key).append("=").append(tableRr.bind==':' ? ":1":"?");

	qryCnt.append("select ").append(Field).append(" from ").append(tbl).append(" where ").append(tableRr.key).append("=").append(tableRr.bind==':' ? ":1":"?");

	InsCnt.append("insert into ").append(tbl).append("(").append(Field).append(") values(").append(Value).append(")");

	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] QueryKey[%s]", tbl.c_str(), qryKey.c_str());
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] DeleteKey[%s]", tbl.c_str(), delKey.c_str());
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] QueryCnt[%s]", tbl.c_str(), qryCnt.c_str());
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] InsertCnt[%s]", tbl.c_str(), InsCnt.c_str());

	UDBSQL* QueryKey = NULL;
	UDBSQL* DeleteKey = NULL;
	UDBSQL* QueryCnt = NULL;
	UDBSQL* InsertCnt = NULL;
	int ret = 0;

	try
	{
		QueryKey = db1->Stmt(qryKey.c_str());
		if(!QueryKey)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "table[%s] in db[%s] prepare QueryKey failed, sql[%s]", tbl.c_str(), modRr->db, qryKey.c_str());
			goto L1;
		}
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] in db[%s] prepare QueryKey success", tbl.c_str(), modRr->db);
	}
	catch(UDBException& e)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "prepare QueryKey failed, sql[%s], exp[%s]", qryKey.c_str(), e.ToString());
		goto L1;
	}

	try
	{
		DeleteKey = db1->Stmt(delKey.c_str());
		if(!DeleteKey)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "table[%s] in db[%s] prepare DeleteKey failed, sql[%s]", tbl.c_str(), modRr->db, delKey.c_str());
			goto L1;
		}
		DeleteKey->SetBindParam(std::string(1, ktype));
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] in db[%s] prepare DeleteKey success", tbl.c_str(), modRr->db);
	}
	catch(UDBException& e)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "prepare DeleteKey failed, sql[%s], exp[%s]", delKey.c_str(), e.ToString());
		goto L1;
	}

	try
	{
		QueryCnt = db1->Stmt(qryCnt.c_str());
		if(!QueryCnt)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "table[%s] in db[%s] prepare QueryCnt failed, sql[%s]", tbl.c_str(), modRr->db, qryCnt.c_str());
			goto L1;
		}
		QueryCnt->SetBindParam(std::string(1, ktype));
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] in db[%s] prepare QueryCnt success", tbl.c_str(), modRr->db);
	}
	catch(UDBException& e)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "prepare QueryCnt failed, sql[%s], exp[%s]", qryCnt.c_str(), e.ToString());
		goto L1;
	}

	try
	{
		InsertCnt = db2->Stmt(InsCnt.c_str());
		if(!InsertCnt)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "table[%s] in db[%s] prepare InsertCnt failed, sql[%s]", tbl.c_str(), modRr->db2, InsCnt.c_str());
			goto L1;
		}
		InsertCnt->SetBindParam(Param);
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "table[%s] in db[%s] prepare InsertCnt success", tbl.c_str(), modRr->db2);
	}
	catch(UDBException& e)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "prepare InsertCnt failed, sql[%s], exp[%s]", InsCnt.c_str(), e.ToString());
		goto L1;
	}

	ret = do_table_repair_data(tableRr, rDT, tbl, Func, ktype, QueryKey, DeleteKey, QueryCnt, InsertCnt);
	if(ret < 0)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "repair data failed, table[%s]", tbl.c_str());
	}
	else
	{
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "repair data success, table[%s]", tbl.c_str());
	}

L1:
	delete InsertCnt;
	delete QueryCnt;
	delete DeleteKey;
	delete QueryKey;
	return ret;
}

int DCDBManerIMP::do_table_repair_data(DCTableRepair& tableRr, DCRepairDT& rDT, std::string& tbl, std::map<int, std::string>& Func, char ktype,
	UDBSQL*QueryKey, UDBSQL*DeleteKey, UDBSQL*QueryCnt, UDBSQL*InsertCnt)
{
	std::vector<UDBSQL*> vQkey = static_cast<UDBSQLHK*>(QueryKey)->vSql();
	std::vector<UDBSQL*> vDkey = static_cast<UDBSQLHK*>(DeleteKey)->vSql();
	std::vector<UDBSQL*> vQCnt = static_cast<UDBSQLHK*>(QueryCnt)->vSql();
	std::vector<UDBSQL*> vICnt = static_cast<UDBSQLHK*>(InsertCnt)->vSql();

	UDBSQL* Qkey = NULL;
	UDBSQL* Dkey = NULL;
	UDBSQL* QCnt = NULL;
	UDBSQL* ICnt = NULL;

	unsigned int ic = 0;
	unsigned long icount = 0;
	char key[256]={0};
	char cond[256]={0};
	char OCon[256]={0};
	char NCon[256]={0};
	char OTbl[80]={0};
	char NTbl[80]={0};

	std::string dkey;
	std::string sqltext;
	std::vector<std::pair<char, std::string> > vCnt;
	std::vector<std::vector<std::string> > vRKey(vICnt.size());
	for(unsigned int j=0; j < vRKey.size(); j++)
	{
		vRKey[j].reserve(10000);
	}
	vCnt.reserve(tableRr.column.size());

	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "start to repair the data from table[%s], OT[%lu], DT[%lu]", tbl.c_str(), vQkey.size(), vICnt.size());

	for(unsigned int i = 0;  i < vQkey.size(); i++)
	{
		if(rDT.oTB)
		{
			sprintf(OTbl, "%s_%02u", tbl.c_str(), i%rDT.oTB+1);
		}
		else
		{
			strcpy(OTbl, tbl.c_str());
		}

		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "select the key with needed repaired from table[%s], nOT[%u], OCon[%s]", \
			OTbl, i, vQkey[i]->Connection()->GetConStr());

		Qkey = vQkey[i];
		Dkey = vDkey[i];
		QCnt = vQCnt[i];

		try
		{
			for(unsigned int j=0; j < vRKey.size(); j++)
			{
				vRKey[j].clear();
			}

			// find the repaired keys for single table
			strcpy(OCon, Qkey->Connection()->GetConStr());
			char* p = strstr(OCon,"!!!");
			if(p) *p= 0x0;

			Qkey->UnBindParam();
			Qkey->Execute();
			while(Qkey->Next())
			{
				Qkey->GetValue(1, key);
				dkey = key;

				// get dkey by if cond
				if(!tableRr.dif.empty())
				{
					Qkey->GetValue(2, cond);
					if(run_func(Func[-1], cond) == "true")
					{
						dkey = get_dkey(key, tableRr.dkey.c_str());
					}
				}

				// divtable and compare dbs
				ic = string_hash(dkey.c_str())%(unsigned int)vICnt.size();
				strcpy(NCon, vICnt[ic]->Connection()->GetConStr());
				p = strstr(NCon, "!!!");
				if(p)*p = 0x0;
				if(!strcasecmp(OCon, NCon))
				{
					// db is same
					if(!rDT.oTB && !rDT.nTB)
					{
						// table name is same
						continue;
					}
					if(rDT.oTB && rDT.nTB && (i%rDT.oTB) == (ic%rDT.nTB))
					{
						// divtable name is same
						continue;
					}
				}
				vRKey[ic].push_back(key);
			}
		}
		catch(UDBException&e)
		{
			Qkey->GetSqlString(sqltext);
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "query key failed, exp[%s], sql[%s]", e.ToString(), sqltext.c_str());
			return -1;
		}

		for(unsigned int j=0; j < vRKey.size(); j++)
		{
			// start to move data with the nth dest table
			if(rDT.nTB)
			{
				sprintf(NTbl, "%s_%02u", tbl.c_str(), j%rDT.nTB+1);
			}
			else
			{
				strcpy(NTbl, tbl.c_str());
			}

			DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "need move %lu keys from nOT[%u][%s] to nDT[%u][%s], DCon[%s]", \
				vRKey[j].size(), i, OTbl, j,NTbl, vICnt[j]->Connection()->GetConStr());

			ICnt = vICnt[j];
			icount = 0;
			for(unsigned int k = 0; k < vRKey[j].size(); k++)
			{
				std::string& skey = vRKey[j][k];
				try
				{
					// query the repaired data for single table
					bool bok = true;
					QCnt->UnBindParam();
					QCnt->BindParam(1, skey.c_str());
					QCnt->Execute();
					while(QCnt->Next())
					{
						vCnt.clear();
						for(unsigned int c = 0; c < tableRr.column.size(); c++)
						{
							const char* val = QCnt->GetValue(c+1);
							if(!val) val="";
							if(Func.count(c))
							{
								vCnt.push_back(std::pair<int, std::string>(tableRr.column[c].type, run_func(Func[c], val)));
							}
							else if(tableRr.column[c].val.empty())
							{
								vCnt.push_back(std::pair<int, std::string>(tableRr.column[c].type, val));
							}
						}

						try
						{
							// move data to the dest table
							ICnt->UnBindParam();
							int ival = 0;
							long lval = 0;
							for(unsigned int z = 0; z < vCnt.size(); z++)
							{
								switch(vCnt[z].first)
								{
								case '1':
									ival = atoi(vCnt[z].second.c_str());
									ICnt->BindParam(z+1, ival);
									break;
								case '2':
									lval = atol(vCnt[z].second.c_str());
									ICnt->BindParam(z+1, lval);
									break;
								case '3':
									ICnt->BindParam(z+1, vCnt[z].second.c_str());
									break;
								case '4':
									ICnt->BindBlobParam(z+1, vCnt[z].second.c_str());
									break;
								}
							}

							ICnt->Execute();
							ICnt->Connection()->Commit();
							DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "nDT[%u][%s], insert ok with key[%s]", j,NTbl,skey.c_str());
						}
						catch(UDBException&e)
						{
							ICnt->Connection()->Rollback();
							bok = false;
							DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "nDT[%u][%s], insert failed with key[%s]", j,NTbl,skey.c_str());
							break;
						}
					}

					if(bok)
					{
						// delete the moved key
						Dkey->UnBindParam();
						Dkey->BindParam(1, skey.c_str());
						Dkey->Execute();
						Dkey->Connection()->Commit();
						icount++;
						DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "nOT[%u][%s], delete ok with key[%s] ", i,OTbl, skey.c_str());
					}
				}
				catch(UDBException&e)
				{
					Dkey->Connection()->Rollback();
					DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "failed to move the key[%s] with nOT[%u][%s]->nDT[%u][%s], exp[%s]", \
						skey.c_str(), i,OTbl, j,NTbl, e.ToString());
					return -1;
				}
			}
			DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "success to move %lu keys with nOT[%u][%s]->nDT[%u][%s]", \
				icount, i,OTbl, j,NTbl);
		}
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "success to repair the data from table[%s], nOT[%u]", \
			OTbl, i);
	}
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "end to repair the data from table[%s], OT[%lu], DT[%lu]", tbl.c_str(), vQkey.size(), vICnt.size());
	return 0;
}

void DCDBManerIMP::split_string(const std::string& str, char sep, std::vector<std::string>& vec)
{
	size_t pos = 0;
	size_t prev = 0;
	std::string sub;
	bool brun = true;
	vec.clear();
	while(brun)
	{
		pos = str.find(sep, prev);
		if(pos == std::string::npos)
		{
			sub = str.substr(prev);
			brun = false;
		}
		else
		{
			sub = str.substr(prev, pos-prev);
			prev = pos+1;
		}
		vec.push_back(sub);
	}
}

void DCDBManerIMP::replace_string(std::string& sql, const std::string& bef, const std::string& aft)
{
	size_t pos = sql.find(bef);
	while(pos != std::string::npos)
	{
		sql.replace(pos, bef.size(), aft);
		pos = sql.find(bef, pos+aft.size());
	}
}

int DCDBManerIMP::CheckReset(bool normal)
{
	int ret=0;
	UDBSTATE state;
	std::map<std::string, UDBConnection*>::iterator it;
	for(it = m_mDB.begin(); it != m_mDB.end(); it++)
	{
		state = it->second->State(normal);
		if(state == UDBS_DB_UNLINK)
		{
			if(it->second->Reset() < 0)
			{
				ret--;
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "reset db connection failed, name[%s]", it->first.c_str());
			}
			else
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "reset db connection success, name[%s]", it->first.c_str());
			}
		}
		else if(state == UDBS_DB_ANY_LINK)
		{
			// try reset another connection
			it->second->Reset();
		}
	}
	return ret;
}

int DCDBManerIMP::SetSQL(const char* mod, const char* name, const char* sub, const char* param, const char* sqltext)
{
	UDBSQL* sql = NULL;
	if(!mod || !name || !sqltext)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the mod[%s], name[%s] and sqltext[%s] must not be empty", mod, name, sqltext);
		return -1;
	}
	DCModDesc* mdesc = getModDesc(mod);
	if(!mdesc)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find mod desc for mod[%s]", mod);
		return -1;
	}
	std::string fullname;
	std::vector<std::string> vsub;
	if(sub && sub[0])
	{
		split_string(sub, ',', vsub);
	}

	std::map<std::string, int>::iterator idx = m_mIndex.find(name);
	if(idx == m_mIndex.end())
	{
		m_vDesc.push_back(DCModSQL());
		DCModSQL& sdesc = m_vDesc.back();
		sdesc.mod = mod;
		sdesc.name = name;
		sdesc.sub = sub ? sub : "";
		sdesc.param = param ? param : "";
		sdesc.sql = sqltext;

		if(sdesc.sub.empty())
		{
			m_mIndex[sdesc.name] = (int)m_vDesc.size()-1;
			sql = GetSQL(sdesc.name.c_str());
			if(!sql)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "GetSQL stmt failed for[%s]", sdesc.name.c_str());
				return -1;
			}
		}
		else
		{
			for(unsigned int i=0; i<vsub.size(); i++)
			{
				fullname = sdesc.name + std::string("|") + vsub[i];
				m_mIndex[fullname] = (int)m_vDesc.size()-1;
				sql = GetSQL(fullname.c_str());
				if(!sql)
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "GetSQL stmt failed for[%s]", fullname.c_str());
					return -1;
				}
			}
		}
		return 0;
	}

	if(!sub) sub = "";
	if(!param) param = "";

	DCModSQL& desc = m_vDesc[idx->second];

	if(desc.sub == sub && desc.param == param && desc.sql == sqltext)
	{
		return 0;
	}
	std::map<std::string, UDBSQL*>::iterator sit;

	if(!desc.dyn.empty())
	{
		std::multimap<std::string, std::string>::iterator mit = m_mExtd.lower_bound(desc.name);
		std::multimap<std::string, std::string>::iterator med = m_mExtd.upper_bound(desc.name);
		while(mit != med)
		{
			sit = m_mSQL.find(mit->second);
			if(sit != m_mSQL.end())
			{
				delete sit->second;
				m_mSQL.erase(sit);
			}
			mit++;
		}
		m_mExtd.erase(desc.name);

		desc.sub = sub;
		desc.param = param;
		desc.sql = sqltext;
		return 0;
	}

	std::vector<std::string> vsub0;
	if(!desc.sub.empty())
	{
		split_string(desc.sub, ',', vsub0);
		for(unsigned int i=0; i<vsub0.size(); i++)
		{
			fullname = desc.name + std::string("|") + vsub0[i];
			m_mIndex.erase(fullname);
			sit = m_mSQL.find(fullname);
			if(sit != m_mSQL.end())
			{
				delete sit->second;
				m_mSQL.erase(sit);
			}
		}
	}
	else
	{
		sit = m_mSQL.find(desc.name);
		if(sit != m_mSQL.end())
		{
			delete sit->second;
			m_mSQL.erase(sit);
		}
	}

	desc.sub = sub;
	desc.param = param;
	desc.sql = sqltext;

	if(desc.sub.empty())
	{
		sql = GetSQL(desc.name.c_str());
		if(!sql)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "GetSQL stmt failed for[%s]", desc.name.c_str());
			return -1;
		}
	}
	else
	{
		for(unsigned int i=0; i<vsub.size(); i++)
		{
			fullname = desc.name + std::string("|") + vsub[i];
			m_mIndex[fullname] = idx->second;
			sql = GetSQL(fullname.c_str());
			if(!sql)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "GetSQL stmt failed for[%s]", fullname.c_str());
				return -1;
			}
		}
	}
	return 0;
}

int DCDBManerIMP::PreparedSQLByMod(const char* modname)
{
	UDBSQL* sql = NULL;
	std::vector<std::string> vsub;
	std::string name;
	std::vector<DCModSQL>::iterator it;
	for(it = m_vDesc.begin(); it != m_vDesc.end(); it++)
	{
		if(it->mod == modname)
		{
			if(!it->dyn.empty())
			{
				continue;
			}
			else if(it->sub.empty())
			{
				sql = GetSQL(it->name.c_str());
				if(!sql)
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "GetSQL stmt failed for[%s]", it->name.c_str());
					return -1;
				}
			}
			else
			{
				split_string(it->sub, ',', vsub);
				for(unsigned int i=0; i<vsub.size(); i++)
				{
					name = it->name + std::string("|") + vsub[i];
					sql = GetSQL(name.c_str());
					if(!sql)
					{
						DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "GetSQL stmt failed for[%s]", name.c_str());
						return -1;
					}
				}
			}
		}
	}
	return 0;
}

bool DCDBManerIMP::GetSQLDescByName(const char* name, DCSQLDesc& desc)
{
	std::map<std::string, int>::iterator it = m_mIndex.find(name);
	if(it == m_mIndex.end())
	{
		return false;
	}

	DCModSQL& modsql = m_vDesc[it->second];

	desc.name = modsql.name;
	desc.sub = modsql.sub;
	desc.param = modsql.param;
	desc.sql = modsql.sql;
	desc.mod = modsql.mod;

	DCModDesc* mod = getModDesc(modsql.mod.c_str());
	if(mod)
	{
		desc.db = mod->db;
		desc.policy = mod->policy;
	}

	return true;
}

bool DCDBManerIMP::GetSQLDescByMod(const char* name, std::vector<DCSQLDesc>& vdesc)
{
	vdesc.clear();
	std::vector<DCModSQL>::iterator it;
	for(it = m_vDesc.begin(); it != m_vDesc.end(); it++)
	{
		if(it->mod == name)
		{
			vdesc.push_back(DCSQLDesc());
			DCSQLDesc& desc = vdesc.back();

			desc.name = it->name;
			desc.sub = it->sub;
			desc.param = it->param;
			desc.sql = it->sql;
			desc.mod = it->mod;

			DCModDesc* mod = getModDesc(it->mod.c_str());
			if(mod)
			{
				desc.db = mod->db;
				desc.policy = mod->policy;
			}
		}
	}
	return !vdesc.empty();
}

int DCDBManerIMP::createDBSQL()
{
	UDBSQL* sql = NULL;
	DCModDesc* mod = NULL;
	std::vector<std::string> vsub;
	std::string name;
	std::string sqltxt;
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to create prepared sql stmt");
	for(std::vector<DCModSQL>::iterator it = m_vDesc.begin(); it != m_vDesc.end(); it++)
	{
		mod = getModDesc(it->mod.c_str());
		if(strcmp(mod->policy, "must"))
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "skip sql prepare for module[%s], policy[%s]", mod->name, mod->policy);
			continue;
		}

		if(!it->dyn.empty())
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "skip sql prepare for sql[%s], it's an extended sql", it->name.c_str());
			continue;
		}
		else if(it->sub.empty())
		{
			sql = create_sql(mod->db, it->name.c_str(), it->sql.c_str(), it->param.c_str());
			if(!sql)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create prepared sql stmt failed for name[%s]", it->name.c_str());
				return -1;
			}
			m_mSQL[it->name] = sql;
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create prepared sql stmt success for name[%s]", it->name.c_str());
		}
		else
		{
			size_t pos = 0;
			split_string(it->sub, ',', vsub);
			for(unsigned int i=0; i < vsub.size(); i++)
			{
				sqltxt = it->sql;
				pos = sqltxt.find("[@]");
				while(pos != std::string::npos)
				{
					sqltxt.replace(pos, 3, vsub[i]);
					pos = sqltxt.find("[@]", pos+vsub[i].size());
				}
				name = it->name + std::string("|") + vsub[i];
				sql = create_sql(mod->db, name.c_str(), sqltxt.c_str(), it->param.c_str());
				if(!sql)
				{
					DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create prepared sql stmt failed for name[%s]", name.c_str());
					return -1;
				}
				m_mSQL[name] = sql;
				DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create prepared sql stmt success for name[%s]", name.c_str());
			}
		}
	}
	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to create prepared sql stmt, size[%lu]", m_mSQL.size());
	return 0;
}

UDBConnection* DCDBManerIMP::create_conn(const char* dbname)
{
	std::vector<DCDBDesc*> vdesc;
	getDBDesc(dbname, vdesc);
	if(vdesc.empty())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find dbinst info for db[%s]", dbname);
		return NULL;
	}
	DCDBDesc* desc = vdesc[0];

	DCDBImp* imp = getDBImp(desc->caty);
	if(!imp)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find dbimp info for category[%s]", desc->caty);
		return NULL;
	}

	DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "find dbinst[%s], dbimp[%s]", dbname, desc->caty);

	std::vector<DCDivDesc> vDiv;
	getDivDesc(dbname, vDiv);

	UDBConnection* conn = NULL;
	UDBConnection* hkcon = NULL;
	bool bisDiv = false;
	if(vDiv.empty())
	{
		// deal not division
		conn = create_db_conn(imp, desc);
		if(!conn)
		{
			return NULL;
		}
	}
	else
	{
		// deal division
		std::vector<UDBConnection*> vdb;
		std::vector<UDBConnectionDIV::DCDivision> vdi;
		std::vector<std::string> vsub;
		UDBConnectionDIV::DCDivision dv;

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to Connect division dbinst[%s]", dbname);

		// create sdb connection
		for(unsigned int i = 0; i < vdesc.size(); i++)
		{
			conn = create_db_conn(imp, vdesc[i]);
			if(!conn)
			{
				break;
			}
			vdb.push_back(conn);
		}

		if(vdb.size() != vdesc.size())
		{
			// create dbconn failed
			for(unsigned int i = 0; i < vdb.size(); i++)
			{
				delete vdb[i];
			}
			return NULL;
		}

		// create division
		for(unsigned int i = 0; i < vDiv.size(); i++)
		{
			dv.ntbl = vDiv[i].ntbl;
			dv.method = vDiv[i].method;
			dv.mon = vDiv[i].mon;

			if(vdesc.size() == 1)
			{
				dv.sdb = 0;
			}
			else if(!strcmp(vDiv[i].sdb.c_str(), "*"))
			{
				dv.sdb = -1;
			}
			else
			{
				for(unsigned int j = 0; j < vdesc.size(); j++)
				{
					if(!strcmp(vDiv[i].sdb.c_str(), vdesc[j]->sdb))
					{
						dv.sdb = j;
						break;
					}
				}
			}

			if(vDiv[i].sub.empty())
			{
				dv.name = vDiv[i].name;
				vdi.push_back(dv);
			}
			else
			{
				size_t found = vDiv[i].name.find("[@]");
				split_string(vDiv[i].sub, ',', vsub);
				for(unsigned int j = 0; j < vsub.size(); j++)
				{
					dv.name = vDiv[i].name;
					dv.name.replace(found, 3, vsub[j]);
					vdi.push_back(dv);
				}
			}
		}
		bisDiv = true;

		conn = new UDBConnectionDIV(imp->caty, desc->name, imp->version, vdb, vdi);

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to Connect division dbinst[%s]", dbname);
	}

	hkcon = new UDBConnectionHK(conn, &m_stat, bisDiv);

	return hkcon;
}

UDBConnection* DCDBManerIMP::create_db_conn(DCDBImp* imp, DCDBDesc* desc)
{
	UDBConnection* conn = NULL;
	const char* env = NULL;
	const char* dbname = desc->name;
	std::string strn;

	if(desc->sdb[0])
	{
		dbname = desc->sdb;
	}

	if(!desc->env.empty())
	{
		env = desc->env.c_str();
	}

	if(desc->sdsn.empty())
	{
		strn = desc->mdsn;
		conn = imp->create(dbname);

		DCSYSLOG(DCLOG_LEVEL_INFO, 0, "create normal Connection for dbinst[%s]", dbname);
	}
	else
	{
		strn = desc->mdsn + std::string("!!!") + desc->sdsn;
		conn = new UDBConnectionHA(imp->caty, dbname, imp->version, imp->create, m_mode);

		DCSYSLOG(DCLOG_LEVEL_INFO, 0, "create HA Connection for dbinst[%s], dbmode[%d]", dbname, m_mode);
	}

	if(!conn)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "create Connection failed for dbinst[%s]", dbname);
		return NULL;
	}

	try
	{
		conn->Connect(strn.c_str(), env);

		//DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to Connect dbinst[%s], ConStr[%s], env[%s]", \
		//	dbname, strn.c_str(), desc->env.c_str());
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to Connect dbinst[%s], ConStr[***], env[%s]", \
			dbname, /*strn.c_str(),*/ desc->env.c_str());

	}catch(UDBException&e)
	{
		delete conn;
		conn = NULL;
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "failed to Connect dbinst[%s], ConStr[%s], env[%s], exp[%s]", \
			dbname, strn.c_str(), desc->env.c_str(), e.ToString());
	}
	return conn;
}

UDBSQL* DCDBManerIMP::create_demand_sql(const char* name)
{
	std::vector<std::string> vnm;
	int dyn = 0;
	DCModSQL* desc = getModSQLDesc(name);
	if(!desc)
	{
		split_string(name,'|',vnm);
		int ns = vnm.size();
		if( ns<= 1 || ns > 5)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid sql name[%s]", name);
			return NULL;
		}
		desc = getModSQLDesc(vnm[0].c_str());
		if(!desc || desc->dyn.empty())
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "not find sql info for name[%s]", name);
			return NULL;
		}
		int ivd = 1+ (int)desc->dyn.size();
		if(!desc->sub.empty())
		{
			ivd++;
		}
		if(ns != ivd)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "incomplete sql name[%s]", name);
			return NULL;
		}
		dyn = 1;
		if(!desc->sub.empty())
		{
			std::string str = std::string(",")+ desc->sub + std::string(",");
			std::string sb = std::string(",")+ vnm[1] + std::string(",");
			if(str.find(sb) == std::string::npos)
			{
				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "invalid sub in sql name[%s]", name);
				return NULL;
			}
			dyn = 2;
		}
	}
	else if(!desc->dyn.empty())
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "incomplete sql name[%s]", name);
		return NULL;
	}

	DCModDesc* mod = getModDesc(desc->mod.c_str());
	if(!mod)
	{
		//DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "the mod[%s] policy[%s] is not [demand]", mod->name, mod->policy);
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "getModDesc(desc->mod[%s]) return null.", desc->mod.c_str());
		return NULL;
	}

	//sql可能需要做替换 ReplaceProperties 
	string sori = desc->sql;
	int ret = DCParseXml::Instance()->ReplaceProperties(sori,desc->sql);	
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "", "ReplaceProperties failed, key[%s] is invalid.",sori.c_str());
	}
	//DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","ReplaceProperties key[%s] value[%s].",sori.c_str(),desc->sql.c_str());

	DCSYSLOG(DCLOG_LEVEL_TRACE, 0, "sql: mod[%s], dbinst[%s], name[%s], bind[%s], sub[%s], dyn[%s], text[%s]", \
		mod->name, mod->db, name, desc->param.c_str(), desc->sub.c_str(), desc->dyn.c_str(), desc->sql.c_str());

	if(desc->name == name)
	{
		return create_sql(mod->db, name, desc->sql.c_str(), desc->param.c_str());
	}

	char buf[8];
	std::string sub;
	std::string sql = desc->sql;

	if(dyn == 0) sub = name + desc->name.size() + 1;
	if(dyn == 2) sub = vnm[1];

	if(!sub.empty()) replace_string(sql, "[@]", sub);

	for(int i = dyn; dyn && i < (int)vnm.size(); i++)
	{
		sprintf(buf, "[@%c]", desc->dyn[i-dyn]);
		replace_string(sql, buf, vnm[i]);
	}
	UDBSQL* hsql = create_sql(mod->db, name, sql.c_str(), desc->param.c_str());
	if(hsql && dyn)
	{
		m_mExtd.insert(make_pair(desc->name, std::string(name)));
	}
	return hsql;
}

UDBSQL* DCDBManerIMP::create_sql(const char* dbname, const char* name, const char* sqltext, const char* param)
{
	UDBConnectionHK* conn = static_cast<UDBConnectionHK*>(GetConnection(dbname));
	if(!conn)
	{
		return NULL;
	}

	UDBSQL* sql = NULL;
	try
	{
		sql= conn->StmtN(name ,sqltext);
		if(sql)
		{
			sql->SetBindParam(param);

			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "create sql success, dbinst[%s], name[%s], bind[%s], text[%s]", \
				dbname, name, param, sqltext);
		}
		else
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create sql failed, dbinst[%s], name[%s], bind[%s], text[%s]", \
				dbname, name, param, sqltext);
		}
	}catch(UDBException& e)
	{
		delete sql;
		sql = NULL;
		DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create sql failed, dbinst[%s], name[%s], bind[%s], text[%s], exp[%s]", \
			dbname, name, param, sqltext, e.ToString());
	}
	return sql;
}
int DCDBManerIMP::GetAllConnStr(multimap<int,string> &mulConnStr)
{
    mulConnStr.clear();
    int conntype = 0;
    for(int i=0;i<m_vDBDesc.size();i++)
    {
		int nMysqlType = 0;		//0:DMDB 1:MYSQL UDAL 2:MYSQL single
		int nDBType = 0;
		std::map<std::string, std::string> kv;

		if(!m_vDBDesc[i].env.empty())
		{
			parse_kv(m_vDBDesc[i].env, kv);
			nDBType = atoi(kv["DBTYPE"].c_str());	       
			
			if(kv.count("DBJT"))
			{
				nMysqlType = atoi(kv["DBJT"].c_str());
			}
			else if(kv.count("CONTYPE"))
			{
				nMysqlType = atoi(kv["CONTYPE"].c_str());
			}	
		}		       
        if(strcmp(m_vDBDesc[i].caty,"UDCA")==0)
        {
            //IP=**************;PORT=17081;UUID=TEST;ACCTID=tydic;USER=test;PWD=123456
            conntype = CONN_TYPE_DCA;
        }
        else if(strcmp(m_vDBDesc[i].caty,"UDBDRIVER")==0)
        {
			if(nDBType == 0)
			{
				//DSN=ahdmdbtt
				conntype = CONN_TYPE_TT;
			}
			else if(nDBType == 3)
			{
				//xzbill_rat/xzbill_rat_2020@ORA94
				conntype = CONN_TYPE_ORACLE;
			}
			else if(nDBType == 6)
			{
				if (0 == nMysqlType)  //DMDB(libmydrv.so驱动)
				{
					//odbc:mysql//**************:6088/dmdb?user=dmdb&amp;password=dmdb
				    conntype = CONN_TYPE_DMDB;
				}
				else
				{
					//odbc:mysql//**************:8901/DUCC1_BILL_JX?user=dca&amp;password=Tydic2019
				    conntype = CONN_TYPE_MYSQL;
				}				
			}
			else if(nDBType == 7)  //DMDB(libtdaldrv.so驱动)
			{
				//odbc:mysql//**************:6088/dmdb?user=dmdb&amp;password=dmdb
				conntype = CONN_TYPE_DMDB;
			}
            else
            {
                continue;
            }
        }
        else if(strcmp(m_vDBDesc[i].caty,"URMDB")==0)
        {
            //xzbill_rat/xzbill_rat_2020@ORA94
			conntype = CONN_TYPE_ORACLE;
        }
        else if(strcmp(m_vDBDesc[i].caty,"UHBASE")==0)
        {
            //IP=***************;PORT=10086;FETCH=12000;TIMEOUT=10;AUTOCOMMIT=0
			conntype = CONN_TYPE_HBASE;
        }
        else if(strcmp(m_vDBDesc[i].caty,"UM2DB")==0)
        {
            //0
            conntype = CONN_TYPE_M2DB;
        }
		else if(strcmp(m_vDBDesc[i].caty,"UPOSTGRE")==0)
        {
            //user=postgres password=123456 hostaddr=************** port=5432 dbname=postgres
            conntype = CONN_TYPE_PG;
        }		
        else
        {
            continue;
        }
        if(!(m_vDBDesc[i].mdsn.empty()))
            mulConnStr.insert(make_pair(conntype,m_vDBDesc[i].mdsn));
        if(!(m_vDBDesc[i].sdsn.empty()))
            mulConnStr.insert(make_pair(conntype,m_vDBDesc[i].sdsn));
    }
    if(mulConnStr.empty())
        return -1;
    return 0;
}


/*----------------------------------------DCDBManer------------------------------------------------*/
DCDBManer::DCDBManer()
{
	m_imp = new DCDBManerIMP();
}

DCDBManer::~DCDBManer()
{
	delete m_imp;
}

int DCDBManer::Init(const char* sqlf, bool prepared)
{
	return m_imp->Init(sqlf, prepared);
}

void DCDBManer::SetDBMode(DCDBMODE mode)
{
	m_imp->SetDBMode(mode);
}

int DCDBManer::CheckReset()
{
	return m_imp->CheckReset(true);
}

int DCDBManer::FastReset()
{
	return m_imp->CheckReset(false);
}

UDBConnection* DCDBManer::GetModConnection(const char* mod)
{
	return m_imp->GetModConnection(mod);
}

UDBConnection* DCDBManer::GetConnection(const char* dbname)
{
	return m_imp->GetConnection(dbname);
}

UDBSQL* DCDBManer::GetSQL(const char* name)
{
	return m_imp->GetSQL(name);
}

int  DCDBManer::SetSQL(const char* mod, const char* name, const char* sub, const char* param, const char* sqltext)
{
	return m_imp->SetSQL(mod, name, sub, param, sqltext);
}

void DCDBManer::DelSQL(const char* name)
{
	m_imp->DelSQL(name);
}

int  DCDBManer::PreparedSQLByMod(const char* modname)
{
	return m_imp->PreparedSQLByMod(modname);
}

bool DCDBManer::GetSQLDescByName(const char* name, DCSQLDesc& desc)
{
	return m_imp->GetSQLDescByName(name, desc);
}

bool DCDBManer::GetSQLDescByMod(const char* name, std::vector<DCSQLDesc>& desc)
{
	return m_imp->GetSQLDescByMod(name, desc);
}

DCPerfStatistic* DCDBManer::get_statistic()
{
	return m_imp->get_statistic();
}

int DCDBManer::DivRepair(const char* mod)
{
	return m_imp->DivRepair(mod);
}

int DCDBManer::GetAllConnStr(multimap<int,string> &mulConnStr)
{
    return m_imp->GetAllConnStr(mulConnStr);
}

