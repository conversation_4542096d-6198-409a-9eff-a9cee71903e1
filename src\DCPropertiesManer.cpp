#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "DCPropertiesManer.h"
#include "PropertiesParser.h"
//#include "DCLogMacro.h"
#include <sys/stat.h>

using namespace std;
using namespace cppproperties;


static pthread_t s_tno = 0;

static void* Deal(void* arg)
{
	DCPropertiesManer * obj = (DCPropertiesManer*)arg;
	obj->routine();
	return NULL;
}


DCPropertiesManer* DCPropertiesManer::m_pDcProperties = NULL;

DCPropertiesManer* DCPropertiesManer::Instance()
{
    if(m_pDcProperties == NULL)
    {
        m_pDcProperties = new DCPropertiesManer();
    }
    return m_pDcProperties;
}


DCPropertiesManer::DCPropertiesManer()
{
	m_properties = NULL;
	m_isInit = false;
	m_runState = true;

	m_properties_Old.clear();
	m_properties_New.clear();
	m_vsNamespace.clear();
	m_vsFilename.clear();
	m_properties_rept.clear();
	m_properties_Bak.clear();
	pthread_mutex_init(&m_mutex, NULL);

	m_sFilepath = "";
	m_sAppid = "";
	m_sCluster = "";
	m_sNamespace = "";
	m_sPubNamespace = "";
	m_vsXmlFileName.clear();
}

DCPropertiesManer::~DCPropertiesManer()
{
	pthread_mutex_destroy(&m_mutex);
	m_runState = false;
	if(m_isInit)
	{
		cancel(s_tno);
		join(s_tno);
	}

	m_pDcProperties = NULL;
}

//根据环境变量获取默认appid、cluster、namespace
int DCPropertiesManer::InitEnv()
{
	char *szTmp = NULL;
	//环境(ENV_PARAM_ENV)
	szTmp = getenv("ENV_PARAM_ENV");
	if(szTmp == NULL || strlen(szTmp)==0)
	{
		printf("getenv [ENV_PARAM_ENV] failed.");
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getenv [ENV_PARAM_ENV] failed.");
		return -1;
	}
	//判断路径是否有效
	if(access(szTmp,F_OK|R_OK))
	{
		printf("envpath [%s] not exist.",szTmp);
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "envpath [%s] not exist.",szTmp);
		return -1;
	}
	m_sFilepath = szTmp;
	if(szTmp[strlen(szTmp)-1] != '/')
	{
		m_sFilepath += "/";
	}

	//私有命名空间(ENV_PARAM_NAMESPACE)
	szTmp = NULL;
    szTmp = getenv("ENV_PARAM_NAMESPACE");
	if(szTmp == NULL || strlen(szTmp)==0)
	{
		printf("getenv [ENV_PARAM_NAMESPACE] failed.");
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getenv [ENV_PARAM_NAMESPACE] failed.");
		return -1;
	}
	m_sNamespace = szTmp;
	
    //公共命名空间(ENV_PARAM_NAMESPACE_PUBLIC)	
    szTmp = NULL;
    szTmp = getenv("ENV_PARAM_PUB_NAMESPACE");
	if(szTmp == NULL || strlen(szTmp)==0)
	{
		printf("getenv [ENV_PARAM_PUB_NAMESPACE] failed.");
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getenv [ENV_PARAM_PUB_NAMESPACE] failed.");
		return -1;
	}
	m_sPubNamespace = szTmp;
	
    //项目(ENV_PARAM_APPID)
    szTmp = NULL;
    szTmp = getenv("ENV_PARAM_APPID");
	if(szTmp == NULL || strlen(szTmp)==0)
	{
		printf("getenv [ENV_PARAM_APPID] failed.");
		//DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "getenv [ENV_PARAM_APPID] failed.");
		m_sAppid = "";
	}
	else
	{
		m_sAppid = szTmp;
	}
	
    //集群(ENV_PARAM_CLUSTER)
    szTmp = NULL;
    szTmp = getenv("ENV_PARAM_CLUSTER");
	if(szTmp == NULL || strlen(szTmp)==0)
	{
		printf("getenv [ENV_PARAM_CLUSTER] failed.");
		//DCBIZLOG(DCLOG_LEVEL_WARN, -1, "", "getenv [ENV_PARAM_CLUSTER] failed.");
		m_sCluster = "";
	}
	else
	{
		m_sCluster = szTmp;
	}
	
	//DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "getenv env[%s] appid[%s] cluster[%s] PubNamespace[%s] namespace[%s]",m_sFilepath.c_str(),m_sAppid.c_str(),m_sCluster.c_str(),m_sPubNamespace.c_str(),m_sNamespace.c_str());
	return 0;
}

//获取Properties信息
int DCPropertiesManer::InitProperties()
{
	//DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "InitProperties sAppid[%s] sCluster[%s] sNamespace[%s].",sAppid.c_str(),sCluster.c_str(),sNamespace.c_str());

	if(!m_isInit)
	{	
		if(InitEnv())
		{
			return -1;
		}
		if(m_sAppid.empty() || m_sCluster.empty() || m_sNamespace.empty())
		{
			//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InitProperties sAppid[%s]/sCluster[%s]/sNamespace[%s] is null.",sAppid.c_str(),sCluster.c_str(),sNamespace.c_str());
			return -1;
		}
		vector<string> vectmp;
		vectmp.clear();
		vectmp.push_back(m_sPubNamespace);//默认公共空间放0号位
		//拆分namespace并顺序保存 ，格式：aaa,bbb,ccc 
		SplitStr(m_sNamespace,",",vectmp);
		m_vsNamespace.swap(vectmp);

		string sFilename;
		string sKey;
		string orivalue;
		string desvalue;
		int iNamespace = 0;
		m_properties = NULL;
		m_properties_Old.clear();
		m_properties_New.clear();
		m_properties_rept.clear();
		vectmp.clear();
		vector<string> keys;
		std::vector<std::string>::iterator itr;
		std::vector<std::string>::iterator itkey;
		try
		{
			for(itr = m_vsNamespace.begin(); itr != m_vsNamespace.end(); ++itr)
			{
				//拼接文件名
				sFilename = m_sFilepath + m_sAppid + "+" + m_sCluster + "+" + *itr + ".properties";
				vectmp.push_back(sFilename);
				//解析文件
				Properties tmpProperties = PropertiesParser::Read(sFilename);
			
				//遍历value解析替换
				if(iNamespace>0)//iNamespace==0是公共的不存在需要替换的
				{
					keys.clear();
					keys = tmpProperties.GetPropertyNames();
					for(itkey = keys.begin(); itkey != keys.end(); ++itkey)
					{
						sKey = *itkey;
						orivalue = tmpProperties.GetProperty(*itkey);
						ReplaceProperties(&m_properties_New, orivalue, desvalue);
						tmpProperties.AddProperty(*itkey, desvalue);
					}
				}
				iNamespace++;
				map<string, string>::iterator itist;
				map<string, string>::iterator itFind;
				for(itist=tmpProperties.properties.begin(); itist != tmpProperties.properties.end();itist++)
				{
					string strFstTmp = string("/") + itist->first;//增加反斜杠/与XML接口统一
					itFind = m_properties_New.find(strFstTmp);
					if(itFind != m_properties_New.end())
					{				
						m_properties_rept.insert(*itFind);//保存覆盖之前的KEY  			//之后改可以保留文件名
					}
					m_properties_New[strFstTmp]=itist->second;
				}
				
			}
			m_vsFilename.swap(vectmp);
		}
		catch(PropertiesException &err)
		{
	        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Read file[%s] failed.Exception[%s]",sFilename.c_str(),err.what());
			return -1;
	    }
		catch(PropertyNotFoundException &err)
		{
	        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetProperty key[%s] failed, file[%s].Exception[%s]",sKey.c_str(),sFilename.c_str(),err.what());
			return -1;
	    }
		catch(...)
		{
	        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InitProperties failed.");
			return -1;
	    }
		m_properties_Old = m_properties_New;
		m_properties = &m_properties_New;

		pthread_mutex_lock(&m_mutex);
		m_properties_Bak = m_properties_New;
		pthread_mutex_unlock(&m_mutex);

		//启动配置中心文件参数刷新线程
		int nRet = pthread_create(&s_tno, NULL, Deal, this);
		if(nRet < 0)
		{
			//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Create Thread Failed.");
			return -1;
		}
		m_isInit = true;
	}
	
	return 0;
}

int DCPropertiesManer::GetXmlFileName(const char* sName)
{
	//sql,callrel,flow,plugin,dict,5GMessageDict,switch,Tel_number不考虑文件名含这些字符的.xml文件
	string strName = sName;
    string strFileName = sName;
	bool bfind = false;
	if(strFileName.find("/") != string::npos)
    {
        //文件名带路径，需过滤路径
        string::size_type pos = strFileName.find_last_of("/");
        strFileName = strFileName.substr(pos + 1);
	}
	
	if( (strFileName.find("sql")    == string::npos) && 
	   (strFileName.find("callrel") == string::npos) && 
	   (strFileName.find("flow")    == string::npos) && 
	   (strFileName.find("plugin")  == string::npos) && 
	   (strFileName.find("dict")    == string::npos) && 
	   (strFileName.find("5GMessageDict")    == string::npos) && 
	   (strFileName.find("switch")    == string::npos) && 
	   (strFileName.find("Tel_number")    == string::npos) )
	{
		for(std::vector<std::string>::iterator itXml = m_vsXmlFileName.begin(); itXml != m_vsXmlFileName.end(); ++itXml)
		{
			if((*itXml) == strName)
				bfind = true;
		}
		if(!bfind) //没找到
			m_vsXmlFileName.push_back(sName);//保存配置文件名cfg.xml
	}
	
}

void DCPropertiesManer::routine()
{
	int ret = 0;
	long mtime_old = time(NULL);
	long mtime_new = time(NULL);
	struct stat newStat;
	std::vector<std::string>::iterator itr;
	std::vector<std::string>::iterator itXml;
	
	while(m_runState)
	{
		//sleep(5);
		for(int i=0; i<50; ++i)
		{
			usleep(100*1000);
			pthread_testcancel();
		}
		
		bool isAddSpace = false;
		for(itr = m_vsFilename.begin(); itr != m_vsFilename.end(); ++itr)
		{
			ret = stat((*itr).c_str(), &newStat);
			if(ret != 0)
			{
				//cout<<"error: no finded config file["<<m_configPath<<"]"<<endl;
				continue;
			}
			if(newStat.st_mtime > mtime_new)
			{
				mtime_new = newStat.st_mtime;
				
				string strFileName = *itr;
				if(strFileName.find("/") != string::npos)
			    {
			        //文件名带路径，需过滤路径
			        string::size_type pos = strFileName.find_last_of("/");
			        strFileName = strFileName.substr(pos + 1);
				}
				if(strFileName.find("PublicParam")== string::npos)
				{
					isAddSpace = true;
				}
			}
		}
		if(mtime_new > mtime_old)
		{
			mtime_old = mtime_new;
			//配置中心文件更新
			RefreshProperties();

			if(isAddSpace)
			{
	      		for(itXml = m_vsXmlFileName.begin(); itXml != m_vsXmlFileName.end(); ++itXml)
				{
					FILE *fp = NULL; 
			 		fp = fopen((*itXml).c_str(), "a+");
			 		//当私有配置中心文件更新，在配置文件cfg.xml内容最后追加空格，使业务程序能刷新配置
			 		if(fp)
			 		{
			 			fputs(" ", fp);
						fclose(fp);
			 		}
				}
			}
		}
	}
}

int DCPropertiesManer::Destroy()
{
	delete this;
	return 0;
}

//取得key的值
int DCPropertiesManer::GetProperties(std::map<std::string,std::string> *properties,const string sKey,string &sValue)
{
	if(!properties)
	{
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetProperties failed, properties is null");
		return -1;
	}

	 if (properties->find(sKey) == properties->end()) 
	 {
        //std::string msg = sKey + " does not exist";
		return -1;
        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetProperty key[%s] failed.Exception[%s]",sKey.c_str(),msg.c_str());
    }
    sValue = properties->at(sKey);

	
	return 0;
}

//自动识别字符串中的key，并取得value替换后返回
int DCPropertiesManer::ReplaceProperties(const string sOri, string &sDes)
{
	if(ReplaceProperties(m_properties, sOri, sDes))
	{
		return -1;
	}
	return 0;
}

//自动识别字符串中的key，并取得value替换后返回
int DCPropertiesManer::ReplaceProperties(std::map<std::string,std::string> *properties,const string sOri, string &sDes)
{
	//DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ReplaceProperties sOri[%s]",sOri.c_str());
	if(sOri == "")
	{
		sDes = sOri;
		return 0;
	}

	string sDestmp = sOri;
	size_t apos = 0;
	//size_t bpos = 0;
	size_t cpos = 0;
	
	string sKey = "";
	string sValue = "";
	do
	{
		//获得key名关键字，格式：      	$$key$$
		apos = sDestmp.find("$$",0);
		if(apos == string::npos)
		{
			break;
		}	
		cpos = sDestmp.find("$$",apos+2);
		if(cpos == string::npos || cpos-apos <= 2)
		{
			break;
		}		
			
		sKey = string("/") + sDestmp.substr(apos+2,cpos-apos-2);//map中KEY前面都加了反斜杠/
		sValue = "";
		if(GetProperties(properties, sKey, sValue))
		{
			sDes = sDestmp;
			//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "ReplaceProperties failed, key[%s] or Namespace is invalid.",sKey.c_str());
			return -1;
		}
		sDes = sDestmp.substr(0,apos) + sValue + sDestmp.substr(cpos+2);
		sDestmp = sDes;
	}
	while(1);
	
	sDes = sDestmp;	
	return 0;
}

//刷新Properties信息
int DCPropertiesManer::RefreshProperties()
{
	if(!m_properties)
	{
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "RefreshProperties failed, m_properties is null");
		return -1;
	}

	map<std::string,std::string> * properties_tmp = NULL;
	if(m_properties == &m_properties_New)
	{
		properties_tmp = &m_properties_Old;
	}
	else
	{
		properties_tmp = &m_properties_New;
	}

	int iNamespace = 0;
	string sKey;
	string orivalue;
	string desvalue;
	string sFile;
	m_properties_rept.clear();
	
	vector<string> keys;
	std::vector<std::string>::iterator itr;
	std::vector<std::string>::iterator itrkey;
	try
	{
		for(itr = m_vsFilename.begin(); itr != m_vsFilename.end(); ++itr)
		{			
			sFile = *itr;
			Properties tmpProperties = PropertiesParser::Read(*itr);
			//遍历value解析替换
			if(iNamespace>0)//iNamespace==0是公共的不存在需要替换的
			{
				keys.clear();
				keys = tmpProperties.GetPropertyNames();
				for(itrkey = keys.begin(); itrkey != keys.end(); ++itrkey)
				{
					sKey = *itrkey;
					orivalue = tmpProperties.GetProperty(*itrkey);
					ReplaceProperties(properties_tmp,orivalue, desvalue);
					tmpProperties.AddProperty(*itrkey, desvalue);
				}
			}
			iNamespace++;
			map<string, string>::iterator itist;
			map<string, string>::iterator itFind;
			for(itist=tmpProperties.properties.begin(); itist != tmpProperties.properties.end();itist++)
			{
				string strFstTmp = string("/") + itist->first;
				itFind = properties_tmp->find(strFstTmp);
				if(itFind != properties_tmp->end())//
				{				
					m_properties_rept.insert(*itFind);//保存重复的KEY
				}
				(*properties_tmp)[strFstTmp]=itist->second;
			}						
		}
	}
	catch(PropertiesException &err)
	{
        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Read file[%s] failed.Exception[%s]",sFile.c_str(),err.what());
		return -1;
    }
	catch(PropertyNotFoundException &err)
	{
        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetProperty key[%s] failed, file[%s].Exception[%s]",sKey.c_str(),sFile.c_str(),err.what());
		return -1;
    }
	catch(...)
	{
        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "RefreshProperties failed.");
		return -1;
    }

	pthread_mutex_lock(&m_mutex);
	m_properties_Bak.clear();
	m_properties_Bak = *properties_tmp;
	pthread_mutex_unlock(&m_mutex);
	
	m_properties = properties_tmp;
	return 0;
}

int DCPropertiesManer::SplitStr(const string sSrcstr, const string sSplit, vector<string>& vecStr)
{
	string str;
	size_t slen = sSplit.length();
	size_t apos = 0;
	size_t bpos = sSrcstr.find(sSplit);
	while (bpos != string::npos)
	{
		if(bpos > apos+1)
		{
			str = sSrcstr.substr(apos,bpos-apos);
			vecStr.push_back(str);
		}
		apos = bpos + slen;
		bpos = sSrcstr.find(sSplit,apos);
		
	}
	
	str = sSrcstr.substr(apos);
	if(!str.empty())
	{
		vecStr.push_back(str);
	}
	
	return 0;
}



