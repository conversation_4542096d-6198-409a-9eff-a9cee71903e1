<?xml version="1.0" encoding="utf-8"?>
<dcsql>
  <!--db 属性字段解释-->
  <!--字段name：		必选	db实例名字-->
  <!--字段category：	必选	数据库实现名字，与imp.category对应-->
  <!--字段env：		可选	数据库实现的附加参数-->
  <!--字段master：	必选	主连接串-->
  <!--字段standby：	可选	备连接串，可不配置-->

  <!--module 属性字段解释-->
  <!--字段name：		必选	module名字 -->
  <!--字段db：		必选	module里面的sql所用的dbinst名字 -->
  <!--字段policy：	必选	SQL加载策略：must 启动时初始化sql, demand 使用时初始化sql, none 不使用 -->

  <!--sql 属性字段解释-->
  <!--字段name：		必选	sql名字，若存在sub, 则对应名字为 "name|sub" -->
  <!--字段bind: 		必选	绑定参数串，每一位代表一个绑定参数类型，1-int, 2-long, 3-char*, 4-blob -->
  <!--字段sub: 		可选	sql子索引，以逗号分割，针对会话表、累帐表等场景，将sqltext中的"[@]"依次替换成子索引以获取多个sql语句 -->
  <data>
    <dbimp>
      <imp category="UDBDRIVER" version="1.0.0">libdriverutil.so</imp>
    </dbimp>
    <dbinst>
      <db name="DCA" category="UDBDRIVER">
        <env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so</env>
        <master>odbc:mysql//192.168.161.125:8899/ducc_bill_sx?user=ducc&amp;password=ducc@201999</master>
      </db>
      <db name="ORACLE" category="UDBDRIVER">
        <env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so</env>
        <master>odbc:mysql//192.168.161.125:8899/ducc_bill_sx?user=ducc&amp;password=ducc@201999</master>
      </db>
      <db name="MYSQL" category="UDBDRIVER">
        <env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so</env>
        <master>odbc:mysql//192.168.161.125:8899/ducc_bill_sx?user=ducc&amp;password=ducc@201999</master>
      </db>
    </dbinst>
  </data>

  <app>
    <module name="dca" db="DCA" policy="must">
      <sql name="query" bind="">select sleep(0.3)</sql>
      <sql name="query_2" bind="">select sleep(0.1)</sql>
    </module>
    <module name="oracle" db="ORACLE" policy="must">
	    <sql name="update" bind="">select sleep(0.2)</sql>
    </module>
    <module name="mysql" db="MYSQL" policy="must">
      <sql name="insert" bind="">select sleep(0.1)</sql>
    </module>
  </app>

</dcsql>
