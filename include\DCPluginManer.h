#ifndef __DCPLUGINMANER_H__
#define __DCPLUGINMANER_H__

#include "DCBaseFlow.h"

#define DFM_USE_DBM			0x1		/*使用数据库*/
#define DFM_USE_CACHE		0x2		/*使用缓存*/
#define DFM_USE_REFRESH		0x4		/*使用数据刷新*/
#define DFM_USE_NOFLOW		0x8		/*初始不加载流程*/

class DCPerfStatistic;
class DCPluginManerImp;
class DCDBManer;
class DCRFData;
class DCMCastManer;
class DCPluginManer
{
public:
	/* 构造函数 */
	DCPluginManer();

	/* 析构函数 */
    ~DCPluginManer();

	/* 初始化函数，path为配置目录，app_name 为配置文件名前缀 */
    int init(const char* path, const char* app_name, int umode = 0x0);

	/* 加载流程 */
	int load_flow(const char* name);

	/* 获取流程句柄 */
	DCBaseFlow* get_flow(const char* name);

	/* 获取统计信息 */
	DCPerfStatistic* get_statistic(const char* szInstanceName = "");

	/* 获取数据库管理句柄 */
	DCDBManer* get_dbm();

	/* 获取数据刷新管理句柄 */
	DCRFData* get_drf();

	/* 获取组播事件管理句柄 */
	DCMCastManer* get_mcm();

	/* 设置附加句柄 */
    void set_handle(const char* name, void* handle);

	/* 获取附加句柄 */
    void* get_handle(const char* name);

	/* 清空所有信息，需重新初始化 */
	void clear();

private:
	DCPluginManerImp* m_imp;
};

#endif // __DCPLUGINMANER_H__
