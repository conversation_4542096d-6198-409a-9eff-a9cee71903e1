#include "DCMon.h"
#include <pthread.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <string.h>
#include <strings.h>
#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <vector>
#include <string>
#include <utility>
#include <json/json.h>

using namespace std;

static DCMon* g_inst = NULL;

DCMon* DCMon::instance()
{
	if(!g_inst)
	{
		g_inst = new DCMon();
	}
	return g_inst;
}

struct MonHead
{
    std::string 	name;
    std::string		val;
};

struct MonKpi
{
    char			name[32];
    char			sub[32];
    bool			type;
    union{
		long		lval;
		float		fval;
    };
};

struct MonGHead
{
	std::string 		name;
    int					type;
	vector<MonHead>		head;
};

struct MonGroup
{
    int					head;
    std::string			id;
    vector<MonKpi>  	kv;
};

struct DCMonGuard
{
	pthread_mutex_t* m_mutex;
	DCMonGuard(pthread_mutex_t* mutex)
		:m_mutex(mutex)
	{
        pthread_mutex_lock(m_mutex);
	}
	~DCMonGuard()
	{
		pthread_mutex_unlock(m_mutex);
	}
};

class DCMonImp
{
public:
	DCMonImp();
	~DCMonImp();

	int init(int interval_ms, int sock_type, const char* servaddr);

	void head_set(const char*name, const char*value);

	void group_set(const char* group, const char* name, const char* value);

	void kpi_op(int op, const char* group, const char* id, const char* k, const char* ki, long lval, float fval);


private:
	static void* work_routine(void* handle)
	{
		DCMonImp* imp = static_cast<DCMonImp*>(handle);
		imp->svc();
		return NULL;
	}

	void svc();

	int reconnect();

	int net_send(const char* data, size_t sz);

    int udp_send(const char* data, size_t sz);

    int tcp_send(const char* data, size_t sz);

    int get_json_data(std::string& json);

	int SendBySelect(const char* data, size_t sz);

private:
	pthread_t			m_tid;
	pthread_mutex_t		m_mutex;

    int					m_sock_type;
    int					m_sock;
    int					m_interval;
    char				m_serv[80];
	sockaddr_in			m_saddr;

    vector<MonHead> 	m_head;
    vector<MonGHead>    m_ghead;
    vector<MonGroup>    m_group;
    vector<MonGroup>	m_gbak;
};

DCMonImp::DCMonImp()
{
	m_tid = -1;
	m_sock_type = 0;
	m_sock = -1;
	m_interval = 0;
	memset(m_serv, 0, sizeof(m_serv));
	bzero(&m_saddr, sizeof(m_saddr));
	pthread_mutex_init(&m_mutex, NULL);
}

DCMonImp::~DCMonImp()
{
	if(m_tid != -1)
	{
		pthread_cancel(m_tid);
	}
	pthread_mutex_destroy(&m_mutex);
}

int DCMonImp::init(int interval_ms, int sock_type, const char* servaddr)
{
	int ret = 0;
	if(interval_ms <= 0)
	{
		// invalid interval_ms
        return -1;
	}
	m_interval = interval_ms;
	m_sock_type = sock_type==1?1:0;

	if(!servaddr)
	{
		servaddr = getenv("OCS_MON_ADDR");
		if(!servaddr)
		{
			// not find env
			return -1;
		}
	}
    const char* sep = strchr(servaddr, ':');
    if(!sep)
	{
		// invalid servaddr
        return -1;
	}
	strncpy(m_serv, servaddr, sizeof(m_serv)-1);

	int port = atoi(sep+1);
	char serv[80]={0};
    strncpy(serv, servaddr, sep-servaddr);

    if(port <= 0 || port >65535)
	{
        // invalid port
        return -1;
	}

	m_saddr.sin_family = AF_INET;
	m_saddr.sin_port = htons((unsigned short)port);
	if(inet_pton(AF_INET, serv, &m_saddr.sin_addr) <= 0)
	{
		// invalid servaddr
		return -1;
	}

	ret = pthread_create(&m_tid, NULL, work_routine, this);
    if(ret < 0)
	{
		// create thread failed
        return ret;
	}
	return 0;
}

void DCMonImp::svc()
{
    int ret = 0;
    long next_tm = time(NULL)*1000 + m_interval;
    long now_tm = 0;
    struct timeval tmv;
    std::string json;

   // reconnect();
    while(1)
	{
		/*if(m_sock == -1)
		{
			ret = reconnect();
			if(ret < 0)
			{
                sleep(1);
                continue;
			}
		}*/
		gettimeofday(&tmv, NULL);
		now_tm = tmv.tv_sec*1000+tmv.tv_usec/1000;
        if(now_tm < next_tm)
		{
            usleep((next_tm-now_tm)*1000);
            now_tm = next_tm;
		}

        get_json_data(json);

		if(!json.empty())
		{
			json.append("\n");
			//printf("%ld.json:%s",now_tm, json.c_str());
			ret = net_send(json.c_str(), json.size());
	        if(ret < 0)
			{
				//printf("send failed:%s\n", strerror(errno));
				close(m_sock);
				reconnect();
			}
		}
		
		gettimeofday(&tmv, NULL);
		now_tm = tmv.tv_sec*1000+tmv.tv_usec/1000;
        if( next_tm + m_interval <=  now_tm )
		{
			next_tm = now_tm;
		}
		else
		{
			next_tm += m_interval;
		}
	}
}

int DCMonImp::reconnect()
{
	int ret = 0;
	//if(m_sock != -1)
	//{
   //     close(m_sock);
   //     m_sock = -1;
	//}

    if(m_sock_type == 1)
	{
		m_sock = socket(AF_INET, SOCK_STREAM, 0);
		if(m_sock < 0)
		{
            // create socket failed
            return -1;
		}
		int optval = 1;
		setsockopt(m_sock, IPPROTO_TCP, TCP_NODELAY, &optval, sizeof(int));
		setsockopt(m_sock, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(int));
		optval = 128*1024;
		setsockopt(m_sock, SOL_SOCKET, SO_SNDBUF, &optval, sizeof(int));

		ret = connect(m_sock,(struct sockaddr*)&m_saddr,sizeof(m_saddr));
		if(ret < 0)
		{
			close(m_sock);
			m_sock = -1;
			return ret;
		}
	}
	else
	{
		m_sock = socket(AF_INET, SOCK_DGRAM, 0);
		if(m_sock < 0)
		{
            // create socket failed
            return -1;
		}
		int optval = 256*1024;
		setsockopt(m_sock, SOL_SOCKET, SO_SNDBUF, &optval, sizeof(int));

		ret = connect(m_sock,(struct sockaddr*)&m_saddr,sizeof(m_saddr));
		if(ret < 0)
		{
			close(m_sock);
			m_sock = -1;
			return ret;
		}
	}
	return 0;
}

int DCMonImp::net_send(const char* data, size_t sz)
{
    if(m_sock_type == 1)
	{
		return tcp_send(data, sz);
	}
	return udp_send(data, sz);
}

int DCMonImp::udp_send(const char* data, size_t sz)
{
	int ret = sendto(m_sock, data, sz, MSG_NOSIGNAL, NULL, 0);
    if(ret < 0)
	{
        // sendto failed
        return ret;
	}
	return ret;
}

int DCMonImp::tcp_send(const char* data, size_t sz)
{
    int ret = 0;
    int pos = 0;
    while(pos < sz)
	{
        ret = send(m_sock, data+pos, sz-pos, MSG_NOSIGNAL);
        if(ret < 0)
		{
			if(errno == EINTR || errno == EAGAIN)
			{
				return SendBySelect(data,sz);
			}
			// send failed
            return ret;
		}
        pos+=ret;
	}
    return 0;
}

int DCMonImp::SendBySelect(const char* data, size_t sz)
{
	int result = 0;
	while(1)
	{
		int maxfd = 0;
		struct timeval tm = {0};
		tm.tv_sec = 1;
		tm.tv_usec = 0;

		fd_set rset;
		FD_ZERO(&rset);
		FD_SET(m_sock,&rset);
		maxfd = m_sock+1;
		int nRet = ::select(maxfd,&rset,NULL,NULL,&tm);
		if(nRet == 0)
		{
			continue;
		}else if(nRet < 0)
		{
			return -1;
		}
		else 
		{
			if(0 == FD_ISSET(m_sock,&rset))
			{
				int nRet = ::send(m_sock,data,sz,0);
				if(nRet <= 0)
				{
					result = -1;
				}
			}else
			{
				result = -1;
			}

			break;
		}
	}
	return result;
}


void DCMonImp::head_set(const char*name, const char*value)
{
	DCMonGuard guard(&m_mutex);
	int idx = -1;
	for(unsigned int i = 0; i < m_head.size(); i++)
	{
        if(!strcmp(m_head[i].name.c_str(),name))
		{
			idx = i;
			m_head[i].val = value;
			break;
		}
	}
	if(idx == -1)
	{
		MonHead head={name, value};
		m_head.push_back(head);
	}
}

void DCMonImp::group_set(const char* group, const char* name, const char* value)
{
	DCMonGuard guard(&m_mutex);
    int idx = -1;
    for(unsigned int i = 0; i < m_ghead.size(); i++)
	{
        if(!strcmp(m_ghead[i].name.c_str(), group))
		{
			idx = i;
			unsigned int j = 0;
            for(; j < m_ghead[i].head.size(); j++)
			{
                if(!strcmp(m_ghead[i].head[j].name.c_str(), name))
				{
					m_ghead[i].head[j].val = value;
				}
			}
			if(j == m_ghead[i].head.size())
			{
				MonHead head={name, value};
				m_ghead[i].head.push_back(head);
			}
		}
	}
	if(idx == -1)
	{
		MonHead head={name, value};
		m_ghead.push_back(MonGHead());
		MonGHead& gp = m_ghead.back();
		gp.name = group;
		gp.type = 0;
		gp.head.push_back(head);
	}
}

void DCMonImp::kpi_op(int op, const char* group, const char* id, const char* k, const char* ki, long lval, float fval)
{
	DCMonGuard guard(&m_mutex);
	int head = -1;
	int idx  = -1;
	int type = (op&0x1)+1;
	bool vtype = (bool)(op&0x2);
	bool vset  = (bool)(op&0x3);
	if(!ki)ki="";

	for(unsigned int i = 0; i < m_ghead.size(); i++)
	{
        if(!strcmp(m_ghead[i].name.c_str(), group))
		{
			head = i;
			break;
		}
	}

	if(head == -1)
	{
		m_ghead.push_back(MonGHead());
		m_ghead.back().name = group;
		m_ghead.back().type = type;
		head = m_ghead.size()-1;
	}
	else
	{
    	for(unsigned int i = 0; i < m_group.size(); i++)
		{
    	    if(head == m_group[i].head)
			{
				if((id&&!strcmp(m_group[i].id.c_str(), id))
				||(!id&&m_group[i].id.empty()))
				{
					idx = i;
					break;
				}
			}
		}
	}

	if(idx != -1)
	{
		if(!m_ghead[head].type)
		{
			m_ghead[head].type = type;
		}
        else if(m_ghead[head].type != type)
		{
            return;
		}
		MonGroup& gp = m_group[idx];
		idx = -1;
		for(unsigned int j = 0; j < gp.kv.size(); j++)
		{
            if(!strcmp(gp.kv[j].name, k) && !strcmp(gp.kv[j].sub, ki))
			{
				idx = j;
                if(gp.kv[j].type==vtype)
				{
                    if(vtype)
					{
						if(vset)
						{
							gp.kv[j].fval = fval;
						}
						else
						{
							gp.kv[j].fval += fval;
						}
					}
					else
					{
						if(vset)
						{
							gp.kv[j].lval = lval;
						}
						else
						{
							gp.kv[j].lval += lval;
						}
					}
				}
			}
		}
		if(idx == -1)
		{
			MonKpi kpi={0};
            strcpy(kpi.name, k);
            strcpy(kpi.sub, ki);
            kpi.type = vtype;
            if(vtype)
			{
                kpi.fval = fval;
			}
			else
			{
                kpi.lval = lval;
			}
			gp.kv.push_back(kpi);
		}
	}
    else
	{
        if(!m_ghead[head].type)
		{
			m_ghead[head].type = type;
		}
		else if(m_ghead[head].type != type)
		{
            return;
		}
        m_group.push_back(MonGroup());
		MonGroup& gp = m_group.back();
		gp.head = head;
		gp.id = id?id:"";

        MonKpi kpi={0};
		strcpy(kpi.name, k);
        strcpy(kpi.sub, ki);
        kpi.type = vtype;
        if(vtype)
		{
            kpi.fval = fval;
		}
		else
		{
            kpi.lval = lval;
		}
        gp.kv.push_back(kpi);
	}
}

int DCMonImp::get_json_data(std::string& json)
{
	json.clear();
	{
		DCMonGuard guard(&m_mutex);
		if(m_group.empty())
		{
			return 0;
		}
		m_gbak = m_group;
        for(unsigned int i = 0; i < m_group.size(); i++)
		{
            for(unsigned int j = 0; j < m_group[i].kv.size(); j++)
			{
				if(m_ghead[m_group[i].head].type == 1)
				{
					m_group[i].kv[j].lval = 0;
				}
			}
		}
	}

	if(m_gbak.empty())
	{
		return 0;
	}

    json_object* root = NULL;
    json_object* jdatv = NULL;
    json_object* jdat = NULL;
    json_object* jgroup = NULL;
    json_object* jki = NULL;
    json_object* kpg = NULL;
    json_object* jvn = NULL;
    json_object* jval = NULL;

    char buf[64];
    std::string rgm;

	root = json_object_new_object();
	for(unsigned int i = 0; i < m_head.size(); i++)
	{
        jval = json_object_new_string(m_head[i].val.c_str());
        json_object_object_add(root, m_head[i].name.c_str(), jval);
	}

	jdatv = json_object_new_array();
	json_object_object_add(root, "data", jdatv);

	jdat = json_object_new_object();
    for(unsigned int i = 0; i < m_gbak.size(); i++)
	{
        MonGHead& ghead = m_ghead[m_gbak[i].head];

        rgm = ghead.name.substr(0, ghead.name.find('|',0));

		jgroup = json_object_object_get(jdat, ghead.name.c_str());
        if(!jgroup)
		{
            jgroup = json_object_new_object();
			json_object_object_add(jdat, ghead.name.c_str(), jgroup);

			for(unsigned int j = 0; j < ghead.head.size(); j++)
			{
				jval = json_object_new_string(ghead.head[j].val.c_str());
				json_object_object_add(jgroup, ghead.head[j].name.c_str(), jval);
			}

			jval = json_object_new_string(ghead.type==1?"cycle":"state");
			json_object_object_add(jgroup, "type", jval);

			if(!m_gbak[i].id.empty())
			{
				jki = json_object_new_array();
				json_object_object_add(jgroup, rgm.c_str(), jki);

				kpg = json_object_new_object();
				json_object_array_add(jki, kpg);
			}
			else
			{
				kpg = json_object_new_object();
				json_object_object_add(jgroup, rgm.c_str(), kpg);
			}
		}
		else
		{
			if(!m_gbak[i].id.empty())
			{
				jki = json_object_object_get(jgroup, rgm.c_str());
				kpg = json_object_new_object();
				json_object_array_add(jki, kpg);
			}
		}

        if(!m_gbak[i].id.empty())
		{
			jval = json_object_new_string(m_gbak[i].id.c_str());
			json_object_object_add(kpg, "id", jval);
		}
        for(unsigned int j = 0; j < m_gbak[i].kv.size(); j++)
		{
            MonKpi& kpi = m_gbak[i].kv[j];

            if(kpi.type)
			{
				sprintf(buf, "%.2f", kpi.fval);
			}
			else
			{
				sprintf(buf, "%ld", kpi.lval);
			}

			if(kpi.sub[0])
			{
                jvn = json_object_object_get(kpg, kpi.name);
                if(!jvn)
				{
					jvn = json_object_new_object();
					json_object_object_add(kpg, kpi.name, jvn);
				}

                jval = json_object_new_string(buf);
				json_object_object_add(jvn, kpi.sub, jval);
			}
			else
			{
                jval = json_object_new_string(buf);
                json_object_object_add(kpg, kpi.name, jval);
			}
		}
	}

	// jdat object -> array
    json_object_object_foreach(jdat, key, obj)
	{
		json_object_get(obj);
		json_object_array_add(jdatv, obj);
	}
	json_object_put(jdat);

    // get json string
	json = json_object_to_json_string(root);
	json_object_put(root);
	return 0;
}

DCMon::DCMon()
{
    m_imp = new DCMonImp();
}

DCMon::~DCMon()
{
    delete m_imp;
}

int DCMon::init(int interval_ms, int sock_type, const char* servaddr)
{
    return m_imp->init(interval_ms, sock_type, servaddr);
}

void DCMon::head(const char*system, const char*subsys, const char*module)
{
	m_imp->head_set("system", system);
	m_imp->head_set("subsys", subsys);
	m_imp->head_set("module", module);
}

void DCMon::head_set(const char*name, const char*value)
{
	m_imp->head_set(name, value);
}

void DCMon::group_set(const char* group, const char* name, const char* value)
{
	m_imp->group_set(group, name, value);
}

void DCMon::cycle_inc(const char* group, const char* k, const char* ki, long value)
{
	// op = 0b000
    m_imp->kpi_op(0, group, NULL, k, ki, value, 0.0);
}

void DCMon::cycle_set(const char* group, const char* k, const char* ki, long value)
{
	// op = 0b100
	m_imp->kpi_op(4, group, NULL, k, ki, value, 0.0);
}

void DCMon::state_set(const char* group, const char* k, const char* ki, long value)
{
	// op = 0b101
	m_imp->kpi_op(5, group, NULL, k, ki, value, 0.0);
}

void DCMon::state_set(const char* group, const char* k, const char* ki, float value)
{
	// op = 0b111
	m_imp->kpi_op(7, group, NULL, k, ki, 0, value);
}

void DCMon::cycle_array_inc(const char* group, int id, const char* k, const char* ki, long value)
{
	// op = 0b000
	char ids[12]={0};
    sprintf(ids, "%d", id);
    m_imp->kpi_op(0, group, ids, k, ki, value, 0.0);
}


void DCMon::cycle_array_set(const char* group, int id, const char* k, const char* ki, long value)
{
	// op = 0b100
	char ids[12]={0};
    sprintf(ids, "%d", id);
    m_imp->kpi_op(4, group, ids, k, ki, value, 0.0);
}


void DCMon::state_array_set(const char* group, int id, const char* k, const char* ki, long value)
{
	// op = 0b101
	char ids[12]={0};
    sprintf(ids, "%d", id);
    m_imp->kpi_op(5, group, ids, k, ki, value, 0.0);
}


void DCMon::state_array_set(const char* group, int id, const char* k, const char* ki, float value)
{
	// op = 0b111
	char ids[12]={0};
    sprintf(ids, "%d", id);
    m_imp->kpi_op(7, group, ids, k, ki, 0, value);
}


void DCMon::cycle_array_inc(const char* group, const char* id, const char* k, const char* ki, long value)
{
	// op = 0b000
    m_imp->kpi_op(0, group, id, k, ki, value, 0.0);
}


void DCMon::cycle_array_set(const char* group, const char* id, const char* k, const char* ki, long value)
{
	// op = 0b100
    m_imp->kpi_op(4, group, id, k, ki, value, 0.0);
}


void DCMon::state_array_set(const char* group, const char* id, const char* k, const char* ki, long value)
{
	// op = 0b101
    m_imp->kpi_op(5, group, id, k, ki, value, 0.0);
}


void DCMon::state_array_set(const char* group, const char* id, const char* k, const char* ki, float value)
{
	// op = 0b111
    m_imp->kpi_op(7, group, id, k, ki, 0, value);
}
