#include "DCBasePlugin.h"
#include <stdio.h>
#include <utility>

class SamMulti : public DCBasePlugin
{
public:
	SamMulti(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)
        :DCBasePlugin(category,func,version)
	{
	}

	virtual ~SamMulti()
	{

	}

protected:
	virtual int init()
	{
		return 0;
	}

	virtual int process(void* input, void* output)
	{
		printf("do SamMulti \n");
        std::pair<int, int>& in = *(std::pair<int, int>*)input;
		int& ot = *(int*)output;

		ot = in.first * in.second;

		return 0;
	}

private:

};

DYN_PLUGIN_CREATE(SamMulti, "FUNC_ALGO", "FC_Multi", "1.0.0")
