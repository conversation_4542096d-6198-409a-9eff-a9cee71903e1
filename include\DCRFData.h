#ifndef __DCRFDATA_H__
#define __DCRFDATA_H__

enum EBDIDX
{
	EBDIDX0 = 0,		/*数据索引0*/
	EBDIDX1,			/*数据索引1*/
};

class DCDBManer;
class BData
{
public:
	/*构造函数*/
	BData():m_used_idx(EBDIDX0){}

	/*析构函数*/
	virtual ~BData(){}

	/*设置使用数据索引*/
    void set_used(EBDIDX idx)
    {
		m_used_idx = idx;
    }

    /*获取使用数据指针*/
    void* data()
    {
		return data(m_used_idx);
    }

    /*初始化*/
    virtual int init(DCDBManer* dbm) = 0;

	/*按索引更新数据*/
	virtual int work(EBDIDX idx) = 0;

	/*按索引获取数据指针*/
    virtual void* data(EBDIDX idx) = 0;

	/*按索引清空数据*/
    virtual void clear(EBDIDX idx) = 0;

private:
	EBDIDX	m_used_idx;
};

class DCRFDataImp;
class DCRFData
{
public:
	/*获取单例指针*/
	static DCRFData* instance();

	/*析构函数*/
	~DCRFData();

	/*初始化, sqlfile 为sql文件名，nsec 为刷新间隔*/
	int init(const char* sqlfile, int nsec=1800);

	/*注册刷新对象, nsec > 0 使用特定的刷新间隔, 重复注册返回1 */
    int regist(const char* name, BData* base, int nsec = 0);

	/*注销刷新对象*/
    void unregist(const char* name);

	/*按名获取刷新对象*/
    BData* get(const char* name);

    /*按名获取数据*/
    template<typename T >
    T* getval(const char* name)
    {
    	BData* base = get(name);
    	if(base)
		{
			return static_cast<T*>(base->data());
		}
		return (T*)0;
    }

	/*触发手工刷新*/
	void refresh();

	/*设置刷新间隔*/
	void setinval(int nsec);

	/*获取内部数据库管理句柄*/
	DCDBManer* dbm();

private:
	/*初始化*/
	DCRFData();

private:
    static DCRFData* 	m_pinst;
    DCRFDataImp*		m_imp;
};

#endif // __DCRFDATA_H__
