#include "DCPluginManer.h"
#include <stdio.h>
#include <utility>
#include <cassert>
#include <cstdarg>
#include <string.h>

int main(int argc, char*argv[])
{
	int ret = 0;
	DCPluginManer pm;

	if(argc < 3)
	{
		printf("Usage:%s config_path app\n",argv[0]);
		return 1;
	}

	ret = pm.init(argv[1], argv[2]);
	if(ret)
	{
		printf("DCPluginManer init failed:%d\n",ret);
		return 1;
	}

	DCBaseFlow* flow = pm.get_flow("SamAlgo");
	if(!flow)
	{
		printf("not find flow[SamAlgo]\n");
		return 1;
	}

    std::pair<int,int> input(3,4);
    int output = 0;

    ret = flow->call(&input, &output);
    if(ret == UNSUPPORTEDFLOW || ret == UNSUPPORTEDFUNC)
	{
		printf("exist unsupported flow or func plugin\n");
		return 1;
	}
	if(ret)
	{
		printf("other result:%d\n",ret);
		return 1;
	}

	printf("SamAlgo: input [%d],[%d] output[%d]\n",input.first, input.second, output);
	return 0;
}

void DCLogOut(int CLASS, int LEVEL, int err, const char* file, int line, const char* func, const char* key, const char* fmt, ...)
{
    char buf[1024];
    const char* pf = strrchr(file, '\\');
    if(pf) pf++;
    else pf = file;

    sprintf(buf, "ocs|%02d|%d|%d|%s|%d|%s|%s|", CLASS, LEVEL, err, pf, line, func, key);

    va_list args;
    va_start(args,fmt);
    vsprintf(buf+strlen(buf), fmt, args);
    va_end(args);

    puts(buf);
}
