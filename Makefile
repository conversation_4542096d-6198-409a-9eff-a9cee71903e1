ifeq ($(arch),32)
	ARCH := -m32
	BITS := 32
else
	ARCH:= -m64
	BITS:=64
	LIBEXT=64
endif

ifeq ($(release),y)
	OUTTYPE := -o2
	OUTCONFIG := release
else
	OUTCONFIG := debug
endif



TOPDIR:=.
OUTDIR=$(TOPDIR)/lib
OBJDIR=$(TOPDIR)/obj
SRCDIR:=$(TOPDIR)/src
INCDIR:=$(TOPDIR)/include

SOURCE := $(foreach dir,$(SRCDIR), $(wildcard $(dir)/*.cpp))
OBJS := $(filter-out dllmain.o,$(patsubst %.cpp,%.o, $(notdir $(SOURCE))))

#ENCRYPT=../encrypt
#DCLOGCLI=../../Log/LogCliSimple

# set static library name
TLIB :=libtinyxml.a

OUT :=libtinyxml.so
LIBS := pthread
LIBPATH := 
INCLUDEPATH := -I$(INCDIR) 

AR:=ar r
CC= g++
CFLAGS := -g -fPIC -Wno-deprecated -std=c++11 $(OUTTYPE)
LDFLAGS := -shared -fPIC
CXXFLAGS := $(CFLAGS) $(INCLUDEPATH) -D_PWD_ENC_

.PHONY : build clean createdir demo

all:createdir build
createdir:
	mkdir -p $(OUTDIR)
build:$(OUT) $(TLIB)

clean:
	$(RM) $(OUTDIR)/$(OUT) && cd $(OUTDIR) && $(RM) $(OBJS)
	
$(TLIB) : $(OBJS)
	$(AR) $(OUTDIR)/$(TLIB) $(addprefix $(OBJDIR)/,$(OBJS))


$(OUT) : $(OBJS)
	$(CC) -o $(OUTDIR)/$(OUT) $(LDFLAGS) $(addprefix $(OBJDIR)/,$(OBJS)) $(LIBPATH) $(addprefix -l ,$(LIBS))
	cd $(OUTDIR) && tar -zcvf $(OUT).tar.gz $(OUT)

%.o : $(SRCDIR)/%.cpp
	$(CC) -c $(CXXFLAGS) $(CPPFLAGS) $< -o $(OBJDIR)/$@

