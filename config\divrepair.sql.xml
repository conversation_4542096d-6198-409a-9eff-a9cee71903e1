<?xml version="1.0" encoding="ansi"?>
<dcsql>
<data>
	<dbimp>
		<imp category="UDBDRIVER" version="1.0.0" >libdriverutil.so</imp>
	</dbimp>
	<dbinst>	
		<db name="ocsdb1" category="UDBDRIVER" >
			<env>DBTYPE=6;DBMAX=10;DRV=libmydrv.so</env>
			<sdb name="t1">
				<master>odbc:mysql//192.168.161.178:4048/ocs_ah?user=ocs_ah&amp;password=ocs_ah</master>
			</sdb>
		</db>
		<db name="ocsdb2" category="UDBDRIVER" >
			<env>DBTYPE=6;DBMAX=10;DRV=libmydrv.so</env>
            <sdb name="t1">
				<master>odbc:mysql//192.168.161.178:4048/ocs_ah?user=ocs_ah&amp;password=ocs_ah</master>
			</sdb>
      	</db>
  </dbinst>
	<division dbinst="ocsdb1" >
		<table name="SESSION_INFORMATION_120"  ntbl="0" />
		<table name="SESSION_INFORMATION_60"   ntbl="0" />
		<table name="SESSION_INFORMATION_80"   ntbl="0" />
		<table name="SESSION_INFORMATION_LTE"  ntbl="0" />
		<table name="SM_DATA_SESSION_STORE"    ntbl="0" />
		<table name="SM_DSL_SESSION_STORE"     ntbl="0" />
		<table name="SM_ISMP_SESSION_STORE"    ntbl="0" />
		<table name="SM_P2PSMS_STORE"          ntbl="0" />
		<table name="SM_PGW_SESSION_STORE"     ntbl="0" />
		<table name="SM_VOICE_SESSION_STORE"   ntbl="0" />
	</division>
	<division dbinst="ocsdb2" >
		<table name="SESSION_INFORMATION_120"  ntbl="0" />
		<table name="SESSION_INFORMATION_60"   ntbl="0" />
		<table name="SESSION_INFORMATION_80"   ntbl="0" />
		<table name="SESSION_INFORMATION_LTE"  ntbl="2" />
		<table name="SM_DATA_SESSION_STORE"    ntbl="0" />
		<table name="SM_DSL_SESSION_STORE"     ntbl="0" />
		<table name="SM_ISMP_SESSION_STORE"    ntbl="0" />
		<table name="SM_P2PSMS_STORE"          ntbl="0" />
		<table name="SM_PGW_SESSION_STORE"     ntbl="2" />
		<table name="SM_VOICE_SESSION_STORE"   ntbl="0" />
	</division>
</data>
<app>
</app>
<repair>
	<module name="REIN" db_from="ocsdb1" db_to="ocsdb2" policy="must">
		<table name="SESSION_INFORMATION_[@]" sub="60,120" key="SESSION_ID">
			<column bind_value="?">
				<col name="SESSION_INFO_ID" 		type="2"/>
				<col name="SESSION_ID" 				type="3"/>
				<col name="PRD_INST_ID" 			type="2"/>
				<col name="LATN_ID" 				type="1"/>
				<col name="UNUSED_AMOUNT" 			type="2"/>
				<col name="USED_AMOUNT" 			type="2"/>
				<col name="RESERVED_AMOUNT" 		type="2"/>
				<col name="UNIT_TYPE" 				type="1"/>
				<col name="SERVICE_NBR" 			type="3"/>
				<col name="AREA_CODE" 				type="3"/>
				<col name="SWITCHPOINT_START" 		type="2"/>
				<col name="SWITCHPOINT_END" 		type="2"/>
				<col name="MODIFY_TIME" 			type="3"/>
				<col name="EVENT_TYPE" 				type="1"/>
				<col name="SESSION_STATUS" 			type="2"/>
				<col name="CURRENT_TIME" 			type="3"/>
				<col name="FREE_STATE" 				type="1"/>
				<col name="UNUSED_MONEY" 			type="3"/>
				<col name="MSG_TYPE" 				type="1"/>
				<col name="PRICING_COMBINE_GROUP_ID" type="3"/>
				<col name="PRE_REQ_RATABLE_INFO" 	type="3"/>
				<col name="UPDATE_DISC_RESULT" 		type="3"/>
				<col name="RESERVEDINFO" 			type="3"/>
			</column>
		</table>
	</module>
	<module name="REDATA" db_from="ocsdb1" db_to="ocsdb2" policy="must">
		<table name="SESSION_INFORMATION_[@]" sub="80" key="SESSION_ID">
			<column bind_value="?">
				<col name="SESSION_INFO_ID" 		type="2"/>
				<col name="SESSION_ID" 				type="3"/>
				<col name="PRD_INST_ID" 			type="2"/>
				<col name="LATN_ID" 				type="1"/>
				<col name="UNUSED_AMOUNT" 			type="2"/>
				<col name="USED_AMOUNT" 			type="2"/>
				<col name="RESERVED_AMOUNT" 		type="2"/>
				<col name="UNIT_TYPE" 				type="1"/>
				<col name="SERVICE_NBR" 			type="3"/>
				<col name="AREA_CODE" 				type="3"/>
				<col name="SWITCHPOINT_START" 		type="2"/>
				<col name="SWITCHPOINT_END" 		type="2"/>
				<col name="MODIFY_TIME" 			type="3"/>
				<col name="EVENT_TYPE" 				type="1"/>
				<col name="SESSION_STATUS" 			type="2"/>
				<col name="CURRENT_TIME" 			type="3"/>
				<col name="FREE_STATE" 				type="1"/>
				<col name="UNUSED_MONEY" 			type="3"/>
				<col name="MSG_TYPE" 				type="1"/>
				<col name="PRICING_COMBINE_GROUP_ID" type="3"/>
				<col name="PRE_REQ_RATABLE_INFO" 	type="3"/>
				<col name="UPDATE_DISC_RESULT" 		type="3"/>
				<col name="RESERVEDINFO" 			type="3"/>
			</column>
		</table>
	</module>
	<module name="RELTE" db_from="ocsdb1" db_to="ocsdb2" policy="must">
		<table name="SESSION_INFORMATION_LTE"  key="MSG_ID">
			<column bind_value="?">
				<col name="SESSION_INFO_ID" 		type="2"/>
				<col name="SESSION_ID" 				type="3"/>
				<col name="PRD_INST_ID" 			type="2"/>
				<col name="LATN_ID" 				type="1"/>
				<col name="UNUSED_AMOUNT" 			type="2"/>
				<col name="USED_AMOUNT" 			type="2"/>
				<col name="RESERVED_AMOUNT" 		type="2"/>
				<col name="UNIT_TYPE" 				type="1"/>
				<col name="SERVICE_NBR" 			type="3"/>
				<col name="AREA_CODE" 				type="3"/>
				<col name="SWITCHPOINT_START" 		type="2"/>
				<col name="SWITCHPOINT_END" 		type="2"/>
				<col name="MODIFY_TIME" 			type="3"/>
				<col name="EVENT_TYPE" 				type="1"/>
				<col name="SESSION_STATUS" 			type="2"/>
				<col name="CURRENT_TIME" 			type="3"/>
				<col name="FREE_STATE" 				type="1"/>
				<col name="UNUSED_MONEY" 			type="3"/>
				<col name="MSG_TYPE" 				type="1"/>
				<col name="PRICING_COMBINE_GROUP_ID" type="3"/>
				<col name="PRE_REQ_RATABLE_INFO" 	type="3"/>
				<col name="UPDATE_DISC_RESULT" 		type="3"/>
				<col name="RESERVEDINFO" 			type="3"/>
				<col name="MSG_ID" 					type="3"/>
			</column>
		</table>
	</module>
	<module name="SM" db_from="ocsdb1" db_to="ocsdb2" policy="must">
		<table name="SM_DATA_SESSION_STORE"  key="OCP_STR_SESSION_ID">
			<dif dkey="[K];%">fn_eq(@SM_INT_DATA_SESSION_FLAG,1)</dif>
			<column bind_value="?">
				<col name="OCP_STR_SESSION_ID" 			type="3"/>
				<col name="OCP_STR_ORIGIN_HOST" 		type="3"/>
				<col name="OCP_STR_TARIFF_TIME_CHANGE" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLING_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLED_NBR" 	type="3"/>
				<col name="OCP_INT_REQ_NBR" 			type="1"/>
				<col name="OCP_LNG_GSU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_TIME" 			type="2"/>
				<col name="OCP_LNG_USU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_TIME" 			type="2"/>
				<col name="OCP_STR_IP_GGSN_ADDR" 		type="3"/>
				<col name="OCP_STR_IP_SGSN_ADDR" 		type="3"/>
				<col name="OCP_STR_IP_PDSN_ADDR" 		type="3"/>
				<col name="OCP_STR_APN_INFO" 			type="3"/>
				<col name="OCP_STR_PDP_TYPE" 			type="3"/>
				<col name="OCP_STR_IP_PDP_ADDR" 		type="3"/>
				<col name="OCP_INT_RATING_GROUP" 		type="2"/>
				<col name="OCP_STR_CPRS_QOS" 			type="3"/>
				<col name="OCP_INT_ROUTE_RECORD" 		type="1"/>
				<col name="OCP_STR_3GPP_RAT_TYPE" 		type="3"/>
				<col name="OCP_STR_LAC_CODE" 			type="3"/>
				<col name="OCP_STR_3GPP_CHARGING_ID" 	type="3"/>
				<col name="OCP_INT_CCA_FLAG" 			type="1"/>
				<col name="OCP_STR_PRODUCT_OFFER_ID" 	type="3"/>
				<col name="OCP_INT_CLASS_IDENTIFIER" 	type="1"/>
				<col name="OCP_STR_CDMA_CHARGING_ID" 	type="3"/>
				<col name="SM_STR_IMSI" 				type="3"/>
				<col name="SM_INT_RESULT_CODE" 			type="1"/>
				<col name="SM_INT_REQ_TYPE" 			type="1"/>
				<col name="SM_LNG_SERIAL" 				type="2"/>
				<col name="SM_INT_SESSION_STATUS" 		type="1"/>
				<col name="SM_LNG_TIME_TO_NEXT_CCR" 	type="2"/>
				<col name="SM_LNG_MSCC_VALIDITY_TIME" 	type="2"/>
				<col name="SM_LNG_QUOTA_CONSUME_TIME" 	type="2"/>
				<col name="SM_LNG_VOLUME_QUOTA_THRESHOLD" type="2"/>
				<col name="SM_LNG_VOLUME_QUOTA_THRESHO_1" type="2"/>
				<col name="SM_LNG_TIME_QUOTA_THRESHOLD" type="2"/>
				<col name="SM_LNG_QUOTA_HOLDING_TIME" 	type="2"/>
				<col name="SM_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_LNG_ALL_USU_MONEY" 		type="2"/>
				<col name="SM_INT_CHARGE_TYPE" 			type="1"/>
				<col name="SM_INT_AOC_TYPE" 			type="1"/>
				<col name="SM_TRACE_NUM_ONFF" 			type="1"/>
				<col name="SM_INT_FREE_FLAG" 			type="1"/>
				<col name="SM_INT_DATA_SESSION_FLAG" 	type="1"/>
				<col name="SM_INT_SESSION_TYPE" 		type="1"/>
				<col name="SM_INT_LOGIN_STATE" 			type="1"/>
				<col name="RE_STR_SUB_NBR" 				type="3"/>
				<col name="RE_STR_SUB_AREA" 			type="3"/>
				<col name="RE_STR_SUB_VISIT_AREA" 		type="3"/>
				<col name="RE_INT_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_ROAM_TYPE" 			type="1"/>
				<col name="RE_LNG_CALL_START_TIME" 		type="2"/>
				<col name="RE_LNG_CURRENT_CCR_TIME" 	type="2"/>
				<col name="RE_LNG_LAST_CCR_TIME" 		type="2"/>
				<col name="RE_INT_PAY_FLAG" 			type="1"/>
				<col name="RE_INT_ISACTIVE_FLAG" 		type="1"/>
				<col name="RE_INT_CALLING_OPERATOR" 	type="1"/>
				<col name="RE_INT_CALLED_OPERATOR" 		type="1"/>
				<col name="RE_LNG_SYS_CCR_TIME" 		type="2"/>
				<col name="RE_INT_LAST_GSU_UNIT" 		type="1"/>
				<col name="RE_LNG_LAST_GSU_TOTAL_OCT" 	type="2"/>
				<col name="RE_LNG_LAST_GSU_TIME" 		type="2"/>
				<col name="CDR_PUB_INT_SEQ" 			type="1"/>
				<col name="CDR_PUB_INT_VERSION" 		type="1"/>
				<col name="CDR_PUB_INT_TICKETTYPE" 		type="1"/>
				<col name="CDR_PUB_STR_TIMESTAMP" 		type="3"/>
				<col name="CDR_PUB_STR_HOSTID" 			type="3"/>
				<col name="CDR_PUB_INT_CORRELATIONID" 	type="1"/>
				<col name="CDR_PUB_INT_TICKETSEQUENCEID" type="1"/>
				<col name="CDR_PUB_INT_SERVICESCENARIOUS" type="1"/>
				<col name="CDR_PUB_STR_CHARGED_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLING_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLED_PARTY" 	type="3"/>
				<col name="CDR_PUB_LNG_SERVID" 			type="2"/>
				<col name="CDR_PUB_LNG_CUSTID" 			type="2"/>
				<col name="CDR_PUB_STR_MASTERPRODUCTID" type="3"/>
				<col name="CDR_PUB_STR_TERMINATE_TIME" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO2" 	type="3"/>
				<col name="CDR_PUB_STR_ACCUMLATORINFO" 	type="3"/>
				<col name="CDR_PUB_STR_TARIFFID" 		type="3"/>
				<col name="CDR_PUB_STR_CHARGEINFO" 		type="3"/>
				<col name="CDR_STR_RATING_GROUP" 		type="3"/>
				<col name="CDR_STR_CELLID" 				type="3"/>
				<col name="CDR_STR_LAC" 				type="3"/>
				<col name="CDR_PUB_STR_PRICING_PLAN_ID" type="3"/>
				<col name="CDR_LNG_EVENT_TYPE_ID" 		type="1"/>
				<col name="CDR_USER_LOCATION_INFO" 		type="3"/>
				<col name="CDR_INT_LONG_FLAG" 			type="1"/>
				<col name="CDR_STR_MVNO_ID" 			type="3"/>
				<col name="CDR_STR_USER_TYPE" 			type="3"/>
				<col name="CDR_DISCOUNT_FEE" 			type="1"/>
				<col name="CDR_OCT_CARD_FLAG" 			type="1"/>
				<col name="CDR_OCT_CARD_TOTAL_VOLUME" 	type="1"/>
				<col name="CDR_PUB_STR_HOSTID_HISTORY" 	type="3"/>
				<col name="CDR_STR_MSC" 				type="3"/>
				<col name="CDR_INT_LATN_ID" 			type="1"/>
				<col name="SM_STR_PLCA_LOC" 			type="3"/>
				<col name="SM_LNG_BALANCE_INFO" 		type="2"/>
				<col name="CDR_ACCT_ID" 				type="2"/>
				<col name="CDR_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_INT_RAR_SEND_FLAG" 		type="2"/>
				<col name="OCP_STR_CONTEXT_ID" 			type="3"/>
			</column>
		</table>
		<table name="SM_PGW_SESSION_STORE"  key="OCP_STR_SESSION_ID">
			<dif dkey="[K];%">fn_eq(@SM_INT_DATA_SESSION_FLAG,1)</dif>
			<column bind_value="?">
				<col name="OCP_STR_SESSION_ID" 			type="3"/>
				<col name="OCP_STR_ORIGIN_HOST" 		type="3"/>
				<col name="OCP_STR_TARIFF_TIME_CHANGE" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLING_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLED_NBR" 	type="3"/>
				<col name="OCP_INT_REQ_NBR" 			type="1"/>
				<col name="OCP_LNG_GSU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_TIME" 			type="2"/>
				<col name="OCP_LNG_USU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_TIME" 			type="2"/>
				<col name="OCP_STR_IP_GGSN_ADDR" 		type="3"/>
				<col name="OCP_STR_IP_SGSN_ADDR" 		type="3"/>
				<col name="OCP_STR_IP_PDSN_ADDR" 		type="3"/>
				<col name="OCP_STR_APN_INFO" 			type="3"/>
				<col name="OCP_STR_PDP_TYPE" 			type="3"/>
				<col name="OCP_STR_IP_PDP_ADDR" 		type="3"/>
				<col name="OCP_INT_RATING_GROUP" 		type="2"/>
				<col name="OCP_STR_CPRS_QOS" 			type="3"/>
				<col name="OCP_INT_ROUTE_RECORD" 		type="1"/>
				<col name="OCP_STR_3GPP_RAT_TYPE" 		type="3"/>
				<col name="OCP_STR_LAC_CODE" 			type="3"/>
				<col name="OCP_STR_3GPP_CHARGING_ID" 	type="3"/>
				<col name="OCP_INT_CCA_FLAG" 			type="1"/>
				<col name="OCP_STR_PRODUCT_OFFER_ID" 	type="3"/>
				<col name="OCP_INT_CLASS_IDENTIFIER" 	type="1"/>
				<col name="OCP_STR_CDMA_CHARGING_ID" 	type="3"/>
				<col name="SM_STR_IMSI" 				type="3"/>
				<col name="SM_INT_RESULT_CODE" 			type="1"/>
				<col name="SM_INT_REQ_TYPE" 			type="1"/>
				<col name="SM_LNG_SERIAL" 				type="2"/>
				<col name="SM_INT_SESSION_STATUS" 		type="1"/>
				<col name="SM_LNG_TIME_TO_NEXT_CCR" 	type="2"/>
				<col name="SM_LNG_MSCC_VALIDITY_TIME" 	type="2"/>
				<col name="SM_LNG_QUOTA_CONSUME_TIME" 	type="2"/>
				<col name="SM_LNG_VOLUME_QUOTA_THRESHOLD" type="2"/>
				<col name="SM_LNG_VOLUME_QUOTA_THRESHO_1" type="2"/>
				<col name="SM_LNG_TIME_QUOTA_THRESHOLD" type="2"/>
				<col name="SM_LNG_QUOTA_HOLDING_TIME" 	type="2"/>
				<col name="SM_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_LNG_ALL_USU_MONEY" 		type="2"/>
				<col name="SM_INT_CHARGE_TYPE" 			type="1"/>
				<col name="SM_INT_AOC_TYPE" 			type="1"/>
				<col name="SM_TRACE_NUM_ONFF" 			type="1"/>
				<col name="SM_INT_FREE_FLAG" 			type="1"/>
				<col name="SM_INT_DATA_SESSION_FLAG" 	type="1"/>
				<col name="SM_INT_SESSION_TYPE" 		type="1"/>
				<col name="SM_INT_LOGIN_STATE" 			type="1"/>
				<col name="RE_STR_SUB_NBR" 				type="3"/>
				<col name="RE_STR_SUB_AREA" 			type="3"/>
				<col name="RE_STR_SUB_VISIT_AREA" 		type="3"/>
				<col name="RE_INT_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_ROAM_TYPE" 			type="1"/>
				<col name="RE_LNG_CALL_START_TIME" 		type="2"/>
				<col name="RE_LNG_CURRENT_CCR_TIME" 	type="2"/>
				<col name="RE_LNG_LAST_CCR_TIME" 		type="2"/>
				<col name="RE_INT_PAY_FLAG" 			type="1"/>
				<col name="RE_INT_ISACTIVE_FLAG" 		type="1"/>
				<col name="RE_INT_CALLING_OPERATOR" 	type="1"/>
				<col name="RE_INT_CALLED_OPERATOR" 		type="1"/>
				<col name="RE_LNG_SYS_CCR_TIME" 		type="2"/>
				<col name="RE_INT_LAST_GSU_UNIT" 		type="1"/>
				<col name="RE_LNG_LAST_GSU_TOTAL_OCT" 	type="2"/>
				<col name="RE_LNG_LAST_GSU_TIME" 		type="2"/>
				<col name="CDR_PUB_INT_SEQ" 			type="1"/>
				<col name="CDR_PUB_INT_VERSION" 		type="1"/>
				<col name="CDR_PUB_INT_TICKETTYPE" 		type="1"/>
				<col name="CDR_PUB_STR_TIMESTAMP" 		type="3"/>
				<col name="CDR_PUB_STR_HOSTID" 			type="3"/>
				<col name="CDR_PUB_INT_CORRELATIONID" 	type="1"/>
				<col name="CDR_PUB_INT_TICKETSEQUENCEID" type="1"/>
				<col name="CDR_PUB_INT_SERVICESCENARIOUS" type="1"/>
				<col name="CDR_PUB_STR_CHARGED_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLING_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLED_PARTY" 	type="3"/>
				<col name="CDR_PUB_LNG_SERVID" 			type="2"/>
				<col name="CDR_PUB_LNG_CUSTID" 			type="2"/>
				<col name="CDR_PUB_STR_MASTERPRODUCTID" type="3"/>
				<col name="CDR_PUB_STR_TERMINATE_TIME" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO2" 	type="3"/>
				<col name="CDR_PUB_STR_ACCUMLATORINFO" 	type="3"/>
				<col name="CDR_PUB_STR_TARIFFID" 		type="3"/>
				<col name="CDR_PUB_STR_CHARGEINFO" 		type="3"/>
				<col name="CDR_STR_RATING_GROUP" 		type="3"/>
				<col name="CDR_STR_CELLID" 				type="3"/>
				<col name="CDR_STR_LAC" 				type="3"/>
				<col name="CDR_PUB_STR_PRICING_PLAN_ID" type="3"/>
				<col name="CDR_LNG_EVENT_TYPE_ID" 		type="1"/>
				<col name="CDR_USER_LOCATION_INFO" 		type="3"/>
				<col name="CDR_INT_LONG_FLAG" 			type="1"/>
				<col name="CDR_STR_MVNO_ID" 			type="3"/>
				<col name="CDR_STR_USER_TYPE" 			type="3"/>
				<col name="CDR_DISCOUNT_FEE" 			type="1"/>
				<col name="CDR_OCT_CARD_FLAG" 			type="1"/>
				<col name="CDR_OCT_CARD_TOTAL_VOLUME" 	type="1"/>
				<col name="CDR_PUB_STR_HOSTID_HISTORY" 	type="3"/>
				<col name="CDR_STR_MSC" 				type="3"/>
				<col name="CDR_INT_LATN_ID" 			type="1"/>
				<col name="SM_STR_PLCA_LOC" 			type="3"/>
				<col name="SM_LNG_BALANCE_INFO" 		type="2"/>
				<col name="CDR_ACCT_ID" 				type="2"/>
				<col name="CDR_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_INT_RAR_SEND_FLAG" 		type="2"/>
				<col name="OCP_STR_CONTEXT_ID" 			type="3"/>
				<col name="OCP_LOCATION_INFO" 			type="3"/>
				<col name="OCP_OLD_LOCATION_EXT" 		type="3"/>
			</column>
		</table>
		<table name="SM_VOICE_SESSION_STORE"  key="OCP_STR_SESSION_ID">
			<column bind_value="?">
				<col name="OCP_STR_SESSION_ID" 			type="3"/>
				<col name="OCP_INT_REQ_NBR" 			type="1"/>
				<col name="OCP_STR_SUB_IDDATA1" 		type="3"/>
				<col name="OCP_STR_ORIGIN_CALLING_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLED_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_HOST" 		type="3"/>
				<col name="OCP_LNG_USU_TIME" 			type="2"/>
				<col name="OCP_LNG_RSU_TIME" 			type="2"/>
				<col name="OCP_INT_ROUTE_RECORD" 		type="1"/>
				<col name="OCP_INT_ACTIVE_FLAG" 		type="1"/>
				<col name="OCP_INT_CCA_FLAG" 			type="1"/>
				<col name="SM_INT_RESULT_CODE" 			type="1"/>
				<col name="SM_LNG_SERIAL" 				type="2"/>
				<col name="SM_INT_REQ_TYPE" 			type="1"/>
				<col name="SM_INT_SESSION_STATUS" 		type="1"/>
				<col name="SM_INT_RETRANSMITFLAG" 		type="1"/>
				<col name="SM_LNG_TIME_TO_NEXT_CCR" 	type="2"/>
				<col name="SM_INT_BILLING_MODE" 		type="1"/>
				<col name="SM_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_STR_CALLING_MSC" 			type="3"/>
				<col name="SM_INT_AOC_TYPE" 			type="1"/>
				<col name="SM_STR_FNFFLAG" 				type="3"/>
				<col name="SM_TRACE_NUM_ONFF" 			type="1"/>
				<col name="SM_INT_FREE_FLAG" 			type="1"/>
				<col name="RE_INT_CALL_TYPE" 			type="1"/>
				<col name="RE_INT_PAY_FLAG" 			type="1"/>
				<col name="RE_INT_IPMOC_FLAG" 			type="1"/>
				<col name="RE_STR_SUB_NBR" 				type="3"/>
				<col name="RE_STR_CALLING_NBR" 			type="3"/>
				<col name="RE_STR_CALLED_NBR" 			type="3"/>
				<col name="RE_STR_SUB_COUNTRY" 			type="3"/>
				<col name="RE_STR_SUB_PROVINCE" 		type="3"/>
				<col name="RE_STR_SUB_AREA" 			type="3"/>
				<col name="RE_STR_CALLING_COUNTRY" 		type="3"/>
				<col name="RE_STR_CALLING_PROVINCE" 	type="3"/>
				<col name="RE_STR_CALLING_AREA" 		type="3"/>
				<col name="RE_STR_CALLED_COUNTRY" 		type="3"/>
				<col name="RE_STR_CALLED_PROVINCE" 		type="3"/>
				<col name="RE_STR_CALLED_AREA" 			type="3"/>
				<col name="RE_STR_CALLED_ACCESS" 		type="3"/>
				<col name="RE_INT_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_CALLING_OPERATOR" 	type="1"/>
				<col name="RE_INT_CALLED_OPERATOR" 		type="1"/>
				<col name="RE_STR_SUB_VISIT_AREA" 		type="3"/>
				<col name="RE_STR_CALLING_VISIT_AREA" 	type="3"/>
				<col name="RE_STR_CALLED_VISIT_AREA" 	type="3"/>
				<col name="RE_INT_LONG_TYPE" 			type="1"/>
				<col name="RE_INT_ROAM_TYPE" 			type="1"/>
				<col name="RE_LNG_CURRENT_CCR_TIME" 	type="2"/>
				<col name="RE_LNG_CALL_START_TIME" 		type="2"/>
				<col name="RE_STR_CALLING_CELL_ID" 		type="3"/>
				<col name="RE_STR_CALLING_LAI" 			type="3"/>
				<col name="RE_STR_CALLED_CELL_ID" 		type="3"/>
				<col name="RE_STR_CALLED_LAI" 			type="3"/>
				<col name="RE_STR_CELL_LAI" 			type="3"/>
				<col name="RE_STR_CELL_ID" 				type="3"/>
				<col name="RE_STR_CALLING_HLR" 			type="3"/>
				<col name="RE_STR_CALLING_VLR" 			type="3"/>
				<col name="RE_STR_CALLED_HLR" 			type="3"/>
				<col name="RE_STR_CALLED_VLR" 			type="3"/>
				<col name="RE_STR_REDIRECT_COUNTRY" 	type="3"/>
				<col name="RE_STR_REDIRECT_PROVINCE" 	type="3"/>
				<col name="RE_STR_REDIRECT_AREA" 		type="3"/>
				<col name="RE_INT_REDIRECT_OPERATOR" 	type="1"/>
				<col name="RE_LNG_SYS_CCR_TIME" 		type="2"/>
				<col name="RE_LNG_LAST_GSU_TIME" 		type="2"/>
				<col name="RE_STR_CALLED_GROP_NUM" 		type="3"/>
				<col name="RE_INT_CALLED_VPN_CALL_TYPE" type="3"/>
				<col name="CDR_PUB_INT_VERSION" 		type="1"/>
				<col name="CDR_PUB_INT_TICKETTYPE" 		type="1"/>
				<col name="CDR_PUB_STR_HOSTID" 			type="3"/>
				<col name="CDR_PUB_INT_CORRELATIONID" 	type="1"/>
				<col name="CDR_PUB_INT_TICKETSEQUENCEID" 	type="1"/>
				<col name="CDR_PUB_INT_SERVICESCENARIOUS" 	type="1"/>
				<col name="CDR_PUB_STR_CHARGED_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLING_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLED_PARTY" 	type="3"/>
				<col name="CDR_PUB_LNG_SERVID" 			type="2"/>
				<col name="CDR_PUB_LNG_CUSTID" 			type="2"/>
				<col name="CDR_PUB_STR_MASTERPRODUCTID" type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO2" 	type="3"/>
				<col name="CDR_PUB_STR_ACCUMLATORINFO" 	type="3"/>
				<col name="CDR_PUB_STR_TARIFFID" 		type="3"/>
				<col name="CDR_PUB_STR_CHARGEINFO" 		type="3"/>
				<col name="CDR_PUB_INT_SEQ" 			type="1"/>
				<col name="CDR_LNG_LAST_ALL_USU" 		type="2"/>
				<col name="CDR_STR_IMSI" 				type="3"/>
				<col name="CDR_LNG_SERVICE_KEY" 		type="2"/>
				<col name="CDR_STR_BEARER_CAPABILITY" 	type="3"/>
				<col name="CDR_STR_REDIRECT_TYPE" 		type="3"/>
				<col name="CDR_STR_REDIRECT_NBR" 		type="3"/>
				<col name="CDR_INT_LONG_FLAG" 			type="1"/>
				<col name="CDR_PUB_STR_PRICING_PLAN_ID" type="3"/>
				<col name="CDR_LNG_EVENT_TYPE_ID" 		type="1"/>
				<col name="CDR_STR_USER_TYPE" 			type="3"/>
				<col name="CDR_STR_MVNO_ID" 			type="3"/>
				<col name="CDR_DISCOUNT_FEE" 			type="1"/>
				<col name="CDR_PUB_STR_HOSTID_HISTORY" 	type="3"/>
				<col name="RE_SUB_VISIT_CARRIER" 		type="3"/>
				<col name="CDR_INT_LATN_ID" 			type="1"/>
				<col name="SM_LNG_BALANCE_INFO" 		type="2"/>
				<col name="CDR_DISCT_CHARGE" 			type="2"/>
				<col name="CDR_ACCT_ID" 				type="2"/>
				<col name="CDR_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="CDR_INT_SPILT_FLAG" 			type="1"/>
				<col name="OCP_STR_CONTEXT_ID" 			type="3"/>
				<col name="OCP_LONG_COST_AMOUNT" 		type="2"/>
				<col name="RE_STR_CALLED_SHORT_NBR" 	type="3"/>
			</column>
		</table>
		<table name="SM_DSL_SESSION_STORE"  key="OCP_STR_SESSION_ID">
			<column bind_value="?">
				<col name="OCP_STR_SESSION_ID" 			type="3"/>
				<col name="OCP_STR_ORIGIN_HOST" 		type="3"/>
				<col name="OCP_INT_REQ_NBR" 			type="1"/>
				<col name="OCP_LNG_USU_TIME" 			type="2"/>
				<col name="OCP_LNG_USU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_STR_TARIFF_TIME_CHANGE" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLING_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLED_NBR" 	type="3"/>
				<col name="OCP_STR_USER_NAME" 			type="3"/>
				<col name="OCP_INT_SUB_TYPE" 			type="1"/>
				<col name="OCP_STR_PRODUCT_SPECID" 		type="3"/>
				<col name="OCP_STR_NAS_IP" 				type="3"/>
				<col name="OCP_STR_FRAMED_IP" 			type="3"/>
				<col name="OCP_STR_USE_NODE_ID" 		type="3"/>
				<col name="OCP_INT_ROUTE_RECORD" 		type="1"/>
				<col name="OCP_INT_CCA_FLAG" 			type="1"/>
				<col name="SM_INT_RESULT_CODE" 			type="1"/>
				<col name="SM_INT_REQ_TYPE" 			type="1"/>
				<col name="SM_LNG_SERIAL" 				type="2"/>
				<col name="SM_INT_SESSION_STATUS" 		type="1"/>
				<col name="SM_LNG_TIME_TO_NEXT_CCR" 	type="2"/>
				<col name="SM_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="SM_INT_CHARGE_TYPE" 			type="1"/>
				<col name="SM_INT_AOC_TYPE" 			type="1"/>
				<col name="SM_INT_RETRANSMITFLAG" 		type="1"/>
				<col name="SM_TRACE_NUM_ONFF" 			type="1"/>
				<col name="SM_INT_FREE_FLAG" 			type="1"/>
				<col name="RE_STR_SUB_NBR" 				type="3"/>
				<col name="RE_STR_SUB_AREA" 			type="3"/>
				<col name="RE_INT_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_SUB_COUNTRY" 			type="1"/>
				<col name="RE_STR_SUB_VISIT_AREA" 		type="3"/>
				<col name="RE_STR_CALLING_NBR" 			type="3"/>
				<col name="RE_STR_CALLED_NBR" 			type="3"/>
				<col name="RE_INT_ROAM_TYPE" 			type="1"/>
				<col name="RE_LNG_CALL_START_TIME" 		type="2"/>
				<col name="RE_LNG_CURRENT_CCR_TIME" 	type="2"/>
				<col name="RE_INT_PAY_FLAG" 			type="1"/>
				<col name="RE_LNG_SYS_CCR_TIME" 		type="2"/>
				<col name="RE_LNG_LAST_GSU_TIME" 		type="2"/>
				<col name="CDR_PUB_INT_SEQ" 			type="1"/>
				<col name="CDR_PUB_INT_VERSION" 		type="1"/>
				<col name="CDR_PUB_INT_TICKETTYPE" 		type="1"/>
				<col name="CDR_PUB_STR_TIMESTAMP" 		type="3"/>
				<col name="CDR_PUB_STR_HOSTID" 			type="3"/>
				<col name="CDR_PUB_INT_CORRELATIONID" 	type="1"/>
				<col name="CDR_PUB_INT_TICKETSEQUENCEID" 	type="1"/>
				<col name="CDR_PUB_INT_SERVICESCENARIOUS" 	type="1"/>
				<col name="CDR_PUB_STR_CHARGED_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLING_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLED_PARTY" 	type="3"/>
				<col name="CDR_PUB_LNG_SERVID" 			type="2"/>
				<col name="CDR_PUB_LNG_CUSTID" 			type="2"/>
				<col name="CDR_PUB_STR_MASTERPRODUCTID" type="3"/>
				<col name="CDR_PUB_STR_TERMINATE_TIME" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO2" 	type="3"/>
				<col name="CDR_PUB_STR_ACCUMLATORINFO" 	type="3"/>
				<col name="CDR_PUB_STR_TARIFFID" 		type="3"/>
				<col name="CDR_PUB_STR_CHARGEINFO" 		type="3"/>
				<col name="CDR_PUB_STR_PRICING_PLAN_ID" type="3"/>
				<col name="CDR_LNG_EVENT_TYPE_ID" 		type="1"/>
				<col name="CDR_STR_USER_TYPE" 			type="3"/>
				<col name="CDR_STR_MVNO_ID" 			type="3"/>
				<col name="CDR_DISCOUNT_FEE" 			type="1"/>
				<col name="CDR_PUB_STR_HOSTID_HISTORY" 	type="3"/>
				<col name="CDR_INT_LATN_ID" 			type="1"/>
				<col name="SM_LNG_BALANCE_INFO" 		type="2"/>
				<col name="CDR_ACCT_ID" 				type="2"/>
				<col name="CDR_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="CDR_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="CDR_INT_SPILT_FLAG" 			type="1"/>
				<col name="OCP_STR_CONTEXT_ID" 			type="3"/>
			</column>
		</table>
		<table name="SM_ISMP_SESSION_STORE"  key="OCP_STR_SESSION_ID">
			<column bind_value="?">
				<col name="OCP_STR_SESSION_ID" 			type="3"/>
				<col name="OCP_STR_ORIGIN_HOST" 		type="3"/>
				<col name="OCP_INT_REQ_NBR" 			type="1"/>
				<col name="OCP_LNG_USU_TIME" 			type="2"/>
				<col name="OCP_LNG_USU_MONEY" 			type="2"/>
				<col name="OCP_LNG_USU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_USU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_TIME" 			type="2"/>
				<col name="OCP_LNG_GSU_MONEY" 			type="2"/>
				<col name="OCP_LNG_GSU_TOTAL_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_INPUT_OCT" 		type="2"/>
				<col name="OCP_LNG_GSU_OUTPUT_OCT" 		type="2"/>
				<col name="OCP_STR_MSG_ID" 				type="3"/>
				<col name="OCP_INT_CHARGE_PARTY_TYPE" 	type="1"/>
				<col name="OCP_STR_ORIGIN_CALLING_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLED_NBR" 	type="3"/>
				<col name="OCP_STR_SP_ID" 				type="3"/>
				<col name="OCP_INT_SERVICE_ENABLE_TYPE" type="1"/>
				<col name="OCP_INT_CHARGING_TYPE" 		type="1"/>
				<col name="OCP_STR_PRODUCT_ID" 			type="3"/>
				<col name="OCP_STR_PRODUCT_OFFER_ID" 	type="3"/>
				<col name="OCP_INT_SERVICE_TYPE" 		type="1"/>
				<col name="OCP_STR_CONTENT_ID" 			type="3"/>
				<col name="OCP_STR_MEDIA_TYPE" 			type="3"/>
				<col name="OCP_STR_IP_CLIENT_IP" 		type="3"/>
				<col name="OCP_INT_ROUTE_RECORD" 		type="1"/>
				<col name="OCP_STR_SPC_PRODUCTID" 		type="3"/>
				<col name="OCP_STR_SP_PRODUCTID" 		type="3"/>
				<col name="OCP_STR_ORDERMETHODID" 		type="3"/>
				<col name="OCP_STR_PUSHID" 				type="3"/>
				<col name="OCP_STR_CPID" 				type="3"/>
				<col name="OCP_INT_CCA_FLAG" 			type="1"/>
				<col name="SM_INT_RESULT_CODE" 			type="1"/>
				<col name="SM_LNG_SERIAL" 				type="2"/>
				<col name="SM_INT_REQ_TYPE" 			type="1"/>
				<col name="SM_INT_SESSION_STATUS" 		type="1"/>
				<col name="SM_INT_RETRANSMITFLAG" 		type="1"/>
				<col name="SM_LNG_TIME_TO_NEXT_CCR" 	type="2"/>
				<col name="SM_INT_BILLING_MODE" 		type="1"/>
				<col name="SM_LNG_ALL_USU_TIME" 		type="2"/>
				<col name="SM_LNG_ALL_USU_MONEY" 		type="2"/>
				<col name="SM_LNG_ALL_USU_TOTAL_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_INPUT_OCT" 	type="2"/>
				<col name="SM_LNG_ALL_USU_OUTPUT_OCT" 	type="2"/>
				<col name="SM_LNG_MSCC_VALIDITY_TIME" 	type="2"/>
				<col name="SM_LNG_QUOTA_CONSUME_TIME" 	type="2"/>
				<col name="SM_LNG_VOLUME_QUOTA_THRESHOLD" 	type="2"/>
				<col name="SM_LNG_TIME_QUOTA_THRESHOLD" type="2"/>
				<col name="SM_LNG_QUOTA_HOLDING_TIME" 	type="2"/>
				<col name="SM_INT_AOC_TYPE" 			type="1"/>
				<col name="SM_TRACE_NUM_ONFF" 			type="1"/>
				<col name="SM_INT_FREE_FLAG" 			type="1"/>
				<col name="RE_INT_PAY_FLAG" 			type="1"/>
				<col name="RE_INT_CHARGE_CNT" 			type="1"/>
				<col name="RE_INT_SUB_TYPE" 			type="1"/>
				<col name="RE_STR_SUB_NBR" 				type="3"/>
				<col name="RE_STR_OA_SUB_NBR" 			type="3"/>
				<col name="RE_STR_DA_SUB_NBR" 			type="3"/>
				<col name="RE_STR_THIRD_PARTY_NBR" 		type="3"/>
				<col name="RE_STR_SUB_COUNTRY" 			type="3"/>
				<col name="RE_STR_SUB_AREA" 			type="3"/>
				<col name="RE_STR_CALLING_COUNTRY" 		type="3"/>
				<col name="RE_STR_CALLING_AREA" 		type="3"/>
				<col name="RE_STR_CALLED_COUNTRY" 		type="3"/>
				<col name="RE_STR_CALLED_AREA" 			type="3"/>
				<col name="RE_STR_THIRD_COUNTRY" 		type="3"/>
				<col name="RE_STR_THIRD_AREA" 			type="3"/>
				<col name="RE_INT_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_CALLING_OPERATOR" 	type="1"/>
				<col name="RE_INT_CALLED_OPERATOR" 		type="1"/>
				<col name="RE_INT_THIRD_OPERATOR" 		type="1"/>
				<col name="RE_LNG_CALL_START_TIME" 		type="2"/>
				<col name="RE_LNG_CURRENT_CCR_TIME" 	type="2"/>
				<col name="RE_LNG_SYS_CCR_TIME" 		type="2"/>
				<col name="CDR_PUB_INT_SEQ" 			type="1"/>
				<col name="CDR_PUB_INT_VERSION" 		type="1"/>
				<col name="CDR_PUB_INT_TICKETTYPE" 		type="1"/>
				<col name="CDR_PUB_STR_TIMESTAMP" 		type="3"/>
				<col name="CDR_PUB_STR_HOSTID" 			type="3"/>
				<col name="CDR_PUB_INT_CORRELATIONID" 	type="1"/>
				<col name="CDR_PUB_INT_TICKETSEQUENCEID" 	type="1"/>
				<col name="CDR_PUB_INT_SERVICESCENARIOUS" 	type="1"/>
				<col name="CDR_PUB_STR_CHARGED_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLING_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLED_PARTY" 	type="3"/>
				<col name="CDR_PUB_LNG_SERVID" 			type="2"/>
				<col name="CDR_PUB_LNG_CUSTID" 			type="2"/>
				<col name="CDR_PUB_STR_MASTERPRODUCTID" type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO2" 	type="3"/>
				<col name="CDR_PUB_STR_ACCUMLATORINFO" 	type="3"/>
				<col name="CDR_PUB_STR_TARIFFID" 		type="3"/>
				<col name="CDR_PUB_STR_CHARGEINFO" 		type="3"/>
				<col name="CDR_STR_MT_MODE" 			type="3"/>
				<col name="CDR_STR_MT_UNII" 			type="3"/>
				<col name="CDR_STR_MT_MONEY_UNIT" 		type="3"/>
				<col name="CDR_STR_MT_TOTAL_MODE" 		type="3"/>
				<col name="CDR_PUB_STR_PRICING_PLAN_ID" type="3"/>
				<col name="CDR_LNG_EVENT_TYPE_ID" 		type="1"/>
				<col name="CDR_STR_USER_TYPE" 			type="3"/>
				<col name="CDR_STR_MVNO_ID" 			type="3"/>
				<col name="CDR_DISCOUNT_FEE" 			type="1"/>
				<col name="CDR_PUB_STR_HOSTID_HISTORY" 	type="3"/>
				<col name="CDR_INT_LATN_ID" 			type="3"/>
				<col name="SM_STR_BALANCE_AOC_TYPE" 	type="3"/>
				<col name="CDR_DISCT_CHARGE" 			type="2"/>
				<col name="CDR_ACCT_ID" 				type="2"/>
				<col name="OCP_STR_CONTEXT_ID" 			type="3"/>
			</column>
		</table>
		<table name="SM_P2PSMS_STORE"  key="OCP_STR_SESSION_ID">
			<column bind_value="?">
				<col name="OCP_STR_SESSION_ID" 			type="3"/>
				<col name="OCP_STR_ORIGIN_HOST" 		type="3"/>
				<col name="OCP_INT_REQ_NBR" 			type="1"/>
				<col name="OCP_STR_ORIGIN_CALLING_NBR" 	type="3"/>
				<col name="OCP_STR_ORIGIN_CALLED_NBR" 	type="3"/>
				<col name="OCP_STR_MSG_ID" 				type="3"/>
				<col name="OCP_STR_SMSC_ADDRESS" 		type="3"/>
				<col name="OCP_INT_MSG_LENGTH" 			type="1"/>
				<col name="OCP_INT_ROUTE_RECORD" 		type="1"/>
				<col name="SM_INT_RESULT_CODE" 			type="1"/>
				<col name="SM_LNG_SERIAL" 				type="2"/>
				<col name="SM_INT_REQ_TYPE" 			type="1"/>
				<col name="SM_INT_SESSION_STATUS" 		type="1"/>
				<col name="SM_LNG_TIME_TO_NEXT_CCR" 	type="2"/>
				<col name="SM_INT_AOC_TYPE" 			type="1"/>
				<col name="SM_TRACE_NUM_ONFF" 			type="1"/>
				<col name="SM_INT_FREE_FLAG" 			type="1"/>
				<col name="RE_LNG_CURRENT_CCR_TIME" 	type="2"/>
				<col name="RE_INT_PAY_FLAG" 			type="1"/>
				<col name="RE_STR_SUB_NBR" 				type="3"/>
				<col name="RE_STR_SUB_COUNTRY" 			type="3"/>
				<col name="RE_STR_SUB_AREA" 			type="3"/>
				<col name="RE_STR_OA_SUB_COUNTRY" 		type="3"/>
				<col name="RE_STR_OA_SUB_AREA" 			type="3"/>
				<col name="RE_STR_DA_SUB_COUNTRY" 		type="3"/>
				<col name="RE_STR_DA_SUB_AREA" 			type="3"/>
				<col name="RE_INT_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_OA_SUB_OPERATOR" 		type="1"/>
				<col name="RE_INT_DA_SUB_OPERATOR" 		type="1"/>
				<col name="RE_LNG_SYS_CCR_TIME" 		type="2"/>
				<col name="CDR_PUB_INT_VERSION" 		type="1"/>
				<col name="CDR_PUB_INT_TICKETTYPE" 		type="1"/>
				<col name="CDR_PUB_STR_HOSTID" 			type="3"/>
				<col name="CDR_PUB_INT_CORRELATIONID" 	type="1"/>
				<col name="CDR_PUB_INT_TICKETSEQUENCEID" 	type="1"/>
				<col name="CDR_PUB_INT_SERVICESCENARIOUS" 	type="1"/>
				<col name="CDR_PUB_STR_CHARGED_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLING_PARTY" 	type="3"/>
				<col name="CDR_PUB_STR_CALLED_PARTY" 	type="3"/>
				<col name="CDR_PUB_LNG_SERVID" 			type="2"/>
				<col name="CDR_PUB_LNG_CUSTID" 			type="2"/>
				<col name="CDR_PUB_STR_MASTERPRODUCTID" type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO" 	type="3"/>
				<col name="CDR_PUB_STR_BALANCEINFO2" 	type="3"/>
				<col name="CDR_PUB_STR_ACCUMLATORINFO" 	type="3"/>
				<col name="CDR_PUB_STR_TARIFFID" 		type="3"/>
				<col name="CDR_PUB_STR_CHARGEINFO" 		type="3"/>
				<col name="CDR_PUB_INT_SEQ" 			type="1"/>
				<col name="CDR_PUB_STR_PRICING_PLAN_ID" type="3"/>
				<col name="CDR_LNG_EVENT_TYPE_ID" 		type="1"/>
				<col name="CDR_MSC_ADDRESS" 			type="3"/>
				<col name="CDR_ROAMINT_TYPE" 			type="1"/>
				<col name="CDR_STR_USER_TYPE" 			type="3"/>
				<col name="CDR_STR_MVNO_ID" 			type="3"/>
				<col name="CDR_DISCOUNT_FEE" 			type="1"/>
				<col name="CDR_INT_LATN_ID" 			type="1"/>
				<col name="SM_STR_BALANCE_AOC_TYPE" 	type="3"/>
				<col name="CDR_ACCT_ID" 				type="2"/>
				<col name="RE_LNG_CALL_START_TIME" 		type="2"/>
				<col name="OCP_STR_CONTEXT_ID" 			type="3"/>
				<col name="RE_STR_OA_SUB_NBR" 			type="3"/>
				<col name="RE_STR_DA_SUB_NBR" 			type="3"/>
			</column>
		</table>
	</module>
</repair>
</dcsql>