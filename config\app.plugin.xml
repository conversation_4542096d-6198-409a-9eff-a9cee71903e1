<?xml version="1.0" encoding="ansi"?>
<dcplugin>
	<plugin  name="FL_MSGREQ" version="1.0.0" category="FLOW_MSGREQ">/path/to/libfl_msgreq.so.1.0.0</plugin>
	<plugin  name="FL_QRYUSERINFO" version="1.0.0" category="FLOW_USERINFO">/path/to/libfl_userinfo.so.1.0.0</plugin>
	<plugin  name="FL_QRYOFRINFO" version="1.0.0" category="FLOW_OFRINFO">/path/to/libfl_ofrinfo.so.1.0.0</plugin>
	<plugin  name="FL_QRYRATABLEINFO" version="1.0.0" category="FLOW_RATABLEINFO">/path/to/libfl_ratableinfo.so.1.0.0</plugin>
	<plugin  name="FL_RATING" version="1.0.0" category="FLOW_RATING">/path/to/libfl_rating.so.1.0.0</plugin>
	<plugin  name="FL_MSGANS" version="1.0.0" category="FLOW_MSGANS">/path/to/libfl_msgans.so.1.0.0</plugin>
	
	<plugin  name="FC_INMSGREQ" version="1.0.0" category="FUNC_MSGREQ">/path/to/libfc_in_msgreq.so.1.0.0</plugin>
	<plugin  name="FC_SMSMSGREQ" version="1.0.0" category="FUNC_MSGREQ">/path/to/libfc_sms_msgreq.so.1.0.0</plugin>
	<plugin  name="FC_DATAMSGREQ" version="1.0.0" category="FUNC_MSGREQ">/path/to/libfc_data_msgreq.so.1.0.0</plugin>
	<plugin  name="FC_ISMPMSGREQ" version="1.0.0" category="FUNC_MSGREQ">/path/to/libfc_ismp_msgreq.so.1.0.0</plugin>
	<plugin  name="FC_DSLMSGREQ" version="1.0.0" category="FUNC_MSGREQ">/path/to/libfc_dsl_msgreq.so.1.0.0</plugin>
	
	<plugin  name="FC_INMSGANS" version="1.0.0" category="FUNC_MSGANS">/path/to/libfc_in_msgans.so.1.0.0</plugin>
	<plugin  name="FC_SMSMSGANS" version="1.0.0" category="FUNC_MSGANS">/path/to/libfc_sms_msgans.so.1.0.0</plugin>
	<plugin  name="FC_DATAMSGANS" version="1.0.0" category="FUNC_MSGANS">/path/to/libfc_data_msgans.so.1.0.0</plugin>
	<plugin  name="FC_ISMPMSGANS" version="1.0.0" category="FUNC_MSGANS">/path/to/libfc_ismp_msgans.so.1.0.0</plugin>
	<plugin  name="FC_DSLMSGANS" version="1.0.0" category="FUNC_MSGANS">/path/to/libfc_dsl_msgans.so.1.0.0</plugin>
	
	<plugin  name="FC_DSLUSER" version="1.0.0" category="FUNC_USERINFO">/path/to/libfc_dsl_userinfo.so.1.0.0</plugin>
	<plugin  name="FC_NORUSER" version="1.0.0" category="FUNC_USERINFO">/path/to/libfc_normal_userinfo.so.1.0.0</plugin>
	<plugin  name="FC_USERATTR" version="1.0.0" category="FUNC_USERATTR">/path/to/libfc_userattr.so.1.0.0</plugin>
	
	<plugin  name="FC_ACELL_INFO" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_acell_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_VCELL_INFO" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_vcell_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_IVPN_INFO" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_ivpn_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_ZONE_CODE" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_zonecode_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_SEC_CARD" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_seccard_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_SPEC_NBR" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_specnbr_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_VPN_NBR" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_vpnnbr_msgenh.so.1.0.0</plugin>
	<plugin  name="FC_FNS_NBR" version="1.0.0" category="FUNC_MSGENH">/path/to/libfc_fnsnbr_msgenh.so.1.0.0</plugin>
	
	<plugin  name="FC_BASE_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_base_ofr.so.1.0.0</plugin>
	<plugin  name="FC_NOR_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_normal_ofr.so.1.0.0</plugin>
	<plugin  name="FC_GROUP_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_group_ofr.so.1.0.0</plugin>
	<plugin  name="FC_SECCARD_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_seccard_ofr.so.1.0.0</plugin>
	<plugin  name="FC_SUBPRD_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_subprd_ofr.so.1.0.0</plugin>
	<plugin  name="FC_IVPN_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_ivpn_ofr.so.1.0.0</plugin>
	<plugin  name="FC_STRATEGY_OFR" version="1.0.0" category="FUNC_OFR">/path/to/libfc_strategy_ofr.so.1.0.0</plugin>
	
	<plugin  name="FC_NOR_RATABLE" version="1.0.0" category="FUNC_RATABLE">/path/to/libfc_normal_ratable.so.1.0.0</plugin>
	<plugin  name="FC_80C_RATABLE" version="1.0.0" category="FUNC_RATABLE">/path/to/libfc_80c_ratable.so.1.0.0</plugin>
	<plugin  name="FC_80I_RATABLE" version="1.0.0" category="FUNC_RATABLE">/path/to/libfc_80i_ratable.so.1.0.0</plugin>
	<plugin  name="FC_80J_RATABLE" version="1.0.0" category="FUNC_RATABLE">/path/to/libfc_80j_ratable.so.1.0.0</plugin>
	
	<plugin  name="FC_PRIORITY_RATING" version="1.0.0" category="FUNC_RATING_MODE">/path/to/libfc_priority_rating.so.1.0.0</plugin>
	<plugin  name="FC_OPTIMAL_RATING" version="1.0.0" category="FUNC_RATING_MODE">/path/to/libfc_optimal_rating.so.1.0.0</plugin>
	
	<plugin  name="FC_STRATEGY_INFO" version="1.0.0" category="FUNC_STRATEGY_INFO">/path/to/libfc_strategy_info.so.1.0.0</plugin>
	<plugin  name="FC_SECTION_INFO" version="1.0.0" category="FUNC_SECTION_INFO">/path/to/libfc_section_info.so.1.0.0</plugin>
	
	<plugin  name="FC_SECTION_REL" version="1.0.0" category="FUNC_SECTION">/path/to/libfc_section_rel.so.1.0.0</plugin>
	<plugin  name="FC_SECTION_COND" version="1.0.0" category="FUNC_SECTION">/path/to/libfc_section_cond.so.1.0.0</plugin>
	<plugin  name="FC_SECTION_CALC" version="1.0.0" category="FUNC_SECTION">/path/to/libfc_section_calc.so.1.0.0</plugin>
	<plugin  name="FC_CYCLE_SECTION_CALC" version="1.0.0" category="FUNC_SECTION">/path/to/libfc_cycle_section_calc.so.1.0.0</plugin>
	
	<plugin  name="FC_FLUX_TARIFF" version="1.0.0" category="FUNC_TARIFF">/path/to/libfc_flux_tariff.so.1.0.0</plugin>
	<plugin  name="FC_TIME_TARIFF" version="1.0.0" category="FUNC_TARIFF">/path/to/libfc_time_tariff.so.1.0.0</plugin>
</dcplugin>