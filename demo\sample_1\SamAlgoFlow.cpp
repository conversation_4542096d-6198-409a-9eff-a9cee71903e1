#include "DCBaseFlow.h"
#include <stdio.h>
#include <utility>

class SamAlgoFlow : public DCBaseFlow
{
public:
	SamAlgoFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)
        :DCBaseFlow(category,func,version)
	{
	}

	virtual ~SamAlgoFlow()
	{

	}

protected:
	virtual int init()
	{
		return 0;
	}

	virtual int process(void* input, void* output)
	{
		printf("do SamAlgoFlow \n");
        return call_one("FLOW_ALGOONE", input, output);
	}

private:

};

DYN_PLUGIN_CREATE(SamAlgoFlow, "FLOW_ALGO", "FLOW_Algo", "1.0.0")

