#include "DCBasePlugin.h"
#include <stdio.h>
#include <utility>

class SamAlgoOne : public DCBasePlugin
{
public:
	SamAlgoOne(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */)
        :DCBasePlugin(category,func,version)
	{
	}

	virtual ~SamAlgoOne()
	{

	}

protected:
	virtual int init()
	{
		return 0;
	}

	virtual int process(void* input, void* output)
	{
		printf("do SamAlgoOne \n");
        std::pair<int, int>& in = *(std::pair<int, int>*)input;
		int& ot = *(int*)output;
		int mi = 0;
		int ret = 0;

		ret = call_one("FC_Add", &in, &mi);
		if(ret == UNSUPPORTEDFUNC)
		{
            printf("unsupported func: FC_Add\n");
		}
		else if(ret)
		{
			printf("FC_Add, result[%d]\n", ret);
			return ret;
		}
		printf("call FC_Add: [%d] + [%d] = [%d]\n", in.first, in.second, mi);

		int mm = 0;
		ret = call_one("FC_Multi", &in, &mm);
		if(ret == UNSUPPORTEDFUNC)
		{
            printf("unsupported func: FC_Multi\n");
		}
		else if(ret)
		{
			printf("FC_Multi, result[%d]\n", ret);
			return ret;
		}
		printf("call FC_Multi: [%d] * [%d] = [%d]\n", in.first, in.second, mm);

		std::pair<int, int> th(mi, mm);
		ret = call_one("FC_Add", &th, &ot);
		if(ret == UNSUPPORTEDFUNC)
		{
            printf("unsupported func: FC_Add\n");
		}
		else if(ret)
		{
			printf("FC_Add, result[%d]\n", ret);
			return ret;
		}
		printf("call FC_Add: [%d] + [%d] = [%d]\n", th.first, th.second, ot);
		return 0;
	}

private:

};

DYN_PLUGIN_CREATE(SamAlgoOne, "FUNC_ALGO", "FL_Algo", "1.0.0")

