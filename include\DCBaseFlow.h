#ifndef __DCBASEFLOW_H__
#define __DCBASEFLOW_H__

#include <vector>
#include <map>
#include <string>
#include "DCBasePlugin.h"

class DCBaseFlow : public DCBasePlugin
{
	friend class DCPluginManerImp;
public:
	/* 构造函数 */
	DCBaseFlow(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */);

	/* 析构函数 */
	virtual ~DCBaseFlow();

	/* 描述简介 */
    virtual const char* desc();

protected:
	/* 实现：功能调用接口 */
    virtual int process(void* input, void* output) = 0;

    /* 实现：按定义调用子流程 */
    virtual int call_all(void* input, void* output);

	/* 实现：调用特定子流程 */
    virtual int call_one(const char* func, void* input, void* output);

    /* 实现：自定义调用子流程 */
    virtual int call_custom(const char* category, const char* func, void* input, void* output){return 0; }

protected:
	struct STFlowItem
    {
        char name[64];
		DCBasePlugin* start;
		std::map<std::string, STFlowItem*> next;
    };

    STFlowItem* get_flowitem(const char* name);

	/* 子流程集合 */
	std::vector<STFlowItem> m_flows;
};

#endif // __DCBASEFLOW_H__
