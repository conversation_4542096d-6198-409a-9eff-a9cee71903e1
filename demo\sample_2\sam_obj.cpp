#include "DCOBJSet.h"
#include "DCLogMacro.h"
#include <cstdio>
#include <cassert>
#include <cstdarg>

struct STA
{
	int a;
	std::string b;
	long c;

	STA()
	{
		a = 0;
		b.clear();
		c = 1;
	}
};

struct STB
{
	int a;
	std::string b;
	long c;

	STB()
	{
		a = 0;
		b.clear();
		c = 1;
	}
};

int main()
{
	DCOBJSetPool* pool = DCOBJSetPool::instance();

	pool->reg<STA>();
	pool->reg<STB>();

	DCOBJSet* pset = pool->get();

	STA* s1 = pset->get<STA>();
	STB* sb = pset->get<STB>();

	printf("a=%d, b=%s\n", s1->a, s1->b.c_str());
	s1->a = 12;
	s1->b = "12345";

	pset->release(); // pool->put(pset);

	pset = pool->get();
	STA* s2 = pset->get<STA>();
	printf("a=%d, b=%s\n", s2->a, s2->b.c_str());

	assert(s1 == s2);

	pset->release();


	DCOBJPool* opl = DCOBJPool::instance();
	opl->reg<STA>();

    DCBOBJ* pobj = opl->get<STA>();

    STA* ps = (STA*)pobj->obj();

	ps->a = 23;
	ps->b = "2345";

	opl->put(pobj);

	__DCLOGIMP__(DCLOG_CLASS_SYS, DCLOG_LEVEL_INFO, 0, __FILE__, __LINE__, __FUNCTION__, "m", "11%s", "123");

	DCSYSLOG(DCLOG_LEVEL_INFO, 0, "12%s", "1234");

	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "m","13%s", "1234");

	DCPERFLOG(0,"14%s", "000");

	DCBILLLOG("BILL", "15%s", "000");

	return 0;
}

void DCLogOut(int CLASS, int LEVEL, int err, const char* file, int line, const char* func, const char* key, const char* fmt, ...)
{
    char buf[1024];
    const char* pf = strrchr(file, '\\');
    if(pf) pf++;
    else pf = file;

    sprintf(buf, "ocs|%02d|%d|%d|%s|%d|%s|%s|", CLASS, LEVEL, err, pf, line, func, key);

    va_list args;
    va_start(args,fmt);
    vsprintf(buf+strlen(buf), fmt, args);
    va_end(args);

    puts(buf);
}
