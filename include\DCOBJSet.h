#ifndef __DCOBJSET_H__
#define __DCOBJSET_H__

#include <typeinfo>
#include <map>
#include <set>
#include <vector>
#include <string>
#include <cstring>

#define DCOBJPOOL_DEFAULT_SIZE		8192
#define DCOBJSETPOOL_DEFAULT_SIZE	4096

class DCBOBJ
{
public:

	virtual ~DCBOBJ(){}

    virtual const char* type() const = 0;

    virtual void* obj() const = 0;

    virtual void clear() = 0;

    virtual DCBOBJ* clone() const = 0;
};

template <class T >
class DCOOBJ: public DCBOBJ
{
public:
	DCOOBJ()
	{
		m_type = typeid(T).name();
	}

	virtual ~DCOOBJ(){}

    virtual const char* type() const
    {
        return m_type;
    }

    virtual void* obj() const
    {
        return (void*)&m_obj;
    }

    virtual void clear()
    {
    		m_obj.~T();
			memset(&m_obj, 0, sizeof(T));
		new (&m_obj) T();
    }

    virtual DCBOBJ* clone() const
    {
        return new DCOOBJ();
    }

private:
	const char* m_type;
	T			m_obj;
};

class DCOBJPool
{
public:
	~DCOBJPool();

	static DCOBJPool* instance();

	template< typename T >
	int reg()
	{
        return reg( DCOOBJ< T >());
	}

    int reg(const DCBOBJ& obj);

	template< typename T >
	DCBOBJ* get()
	{
		return get(typeid(T).name());
	}

	DCBOBJ* get(const char* type);

	void put(DCBOBJ* obj);

	void adjust(size_t max_size);

	size_t  regd_size();

	size_t  used_size();

	size_t  cached_size();

private:
	DCOBJPool();
	DCOBJPool(const DCOBJPool&);
	DCOBJPool& operator=(const DCOBJPool&);

private:
	static DCOBJPool*				m_inst;
	size_t							m_max;
	size_t							m_cad;
	std::map<std::string, DCBOBJ*>	m_regd;
    std::set<DCBOBJ*>				m_used;
	std::map<std::string, std::vector<DCBOBJ*> >	m_cache;
};


class DCOBJSet;
class DCOBJSetPool
{
public:
	~DCOBJSetPool();

	static DCOBJSetPool* instance();

	template< typename T >
	int reg()
	{
        return reg( DCOOBJ< T >());
	}

    int reg(const DCBOBJ& obj);

	DCOBJSet* get();

	void put(DCOBJSet* pset);

	void adjust(size_t max_size);

	size_t  regd_size();

	size_t  used_size();

	size_t  cached_size();

private:
	DCOBJSetPool();
	DCOBJSetPool(const DCOBJSetPool&);
	DCOBJSetPool& operator=(const DCOBJSetPool&);

private:
	static DCOBJSetPool*			m_inst;
	size_t							m_max;
	std::map<std::string, DCBOBJ*>	m_regd;
    std::set<DCOBJSet*>				m_used;
	std::vector<DCOBJSet*>			m_cache;
};

class DCOBJSet
{
	friend class DCOBJSetPool;
public:

	void release()
	{
		DCOBJSetPool::instance()->put(this);
	}

    template <typename T >
    T* get()
    {
		const char* type = typeid(T).name();
		for(std::vector<DCBOBJ*>::iterator it = m_set.begin(); it != m_set.end(); it++)
		{
			if(!strcmp((*it)->type(), type))
			{
				return (T*)(*it)->obj();
			}
		}
		return NULL;
    }

    void clear()
    {
		for(std::vector<DCBOBJ*>::iterator it = m_set.begin(); it != m_set.end(); it++)
		{
            (*it)->clear();
		}
    }

private:
	DCOBJSet(){}

	~DCOBJSet()
	{
		for(std::vector<DCBOBJ*>::iterator it = m_set.begin(); it != m_set.end(); it++)
		{
			delete (*it);
		}
	}

	DCOBJSet(const DCOBJSet&);
	DCOBJSet& operator=(const DCOBJSet&);

private:
    std::vector<DCBOBJ*> m_set;
};

#endif // __DCOBJSET_H__
