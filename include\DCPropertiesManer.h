#ifndef _DC_PROPERTIES_MANER_H_
#define _DC_PROPERTIES_MANER_H_

#include <map>
#include <string>
#include <vector>
#include "Properties.h"
#include "TThreadXml.h"

class DCPropertiesManer : public TThreadXml
{
public:
	DCPropertiesManer();
    ~DCPropertiesManer();

	static DCPropertiesManer* Instance();

	const std::map<std::string,std::string>& DCPropertiesBak()      { return m_properties_Bak; }

	const std::vector<std::string>& DCPropertiesFile()      { return m_vsFilename; }

	//获取Properties信息，从环境变量取namespace
	int InitProperties();

	//自动识别字符串中的key，并取得value替换后返回
	int ReplaceProperties(const std::string sOri, std::string &sDes);

	int GetXmlFileName(const char* sName);

public:
	void routine();

private:
	int Destroy();

	//根据环境变量获取默认appid、cluster、namespace
    int InitEnv();

	//刷新Properties信息
	int RefreshProperties();

	//取得key的值
	int GetProperties(std::map<std::string,std::string> *properties,const std::string sKey, std::string &sValue);

	//自动识别字符串中的key，并取得value替换后返回
	int ReplaceProperties(std::map<std::string,std::string> *properties,const std::string sOri, std::string &sDes);

	//字符串拆分
	int SplitStr(const std::string sSrcstr, const std::string sSplit, std::vector<std::string>& vecStr);

private:
	static DCPropertiesManer* m_pDcProperties;

	bool m_isInit;
	bool m_runState;
	std::string m_sFilepath;
	std::string m_sAppid;//默认项目
	std::string m_sCluster;//默认集群
	std::string m_sNamespace;//程序命名空间
	std::string m_sPubNamespace;//公共命名空间
	std::vector<std::string> m_vsNamespace;
	std::vector<std::string> m_vsFilename;

	std::map<std::string,std::string> *m_properties;
    std::map<std::string,std::string> m_properties_Old;
    std::map<std::string,std::string> m_properties_New;
	std::map<std::string,std::string> m_properties_Bak;
	std::multimap<std::string,std::string> m_properties_rept;//保留重复的KEY

	pthread_mutex_t 		m_mutex;

	std::vector<std::string> m_vsXmlFileName;
};


#endif
