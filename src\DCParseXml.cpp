#include "DCParseXml.h"
#include "tinyxml.h"
#include <iostream>
#include <stdio.h>
#include <string.h>
#include <utility>

#include <stdlib.h>
#include <unistd.h>
#include "PropertiesParser.h"
#include "DCPropertiesManer.h"
#include <sys/stat.h>
//#include "DCLogMacro.h"

using namespace std;
using namespace cppproperties;


static pthread_t s_tno = 0;

static void* Deal(void* arg)
{
	DCParseXml * obj = (DCParseXml*)arg;
	obj->routine();
	return NULL;
}


DCParseXml* DCParseXml::m_pDcParseXml = NULL;

DCParseXml* DCParseXml::Instance()
{
    if(m_pDcParseXml == NULL)
    {
        m_pDcParseXml = new DCParseXml();
    }
    return m_pDcParseXml;
}

DCParseXml::DCParseXml()
{
	m_strConfig = NULL;
	m_strConfig_New.clear();
	m_strConfig_Old.clear();
	m_vModParam.clear();
    memset(m_szFilePath,0,sizeof(m_szFilePath));
	memset(m_szModuleName,0,sizeof(m_szModuleName));

	m_vsFilename.clear();
	m_strConfig_repl.clear();

	m_isInit = false;
	m_runState = true;
}


DCParseXml::~DCParseXml()
{
	m_runState = false;
	if(m_isInit)
	{
		cancel(s_tno);
		join(s_tno);
	}
	delete DCPropertiesManer::Instance();

	m_pDcParseXml = NULL;
}

//取得key的值
int DCParseXml::GetProperties(std::map<std::string,std::string> *properties,const string sKey,string &sValue)
{
	if(!properties)
	{
		//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetProperties failed, properties is null");
		return -1;
	}

	 if (properties->find(sKey) == properties->end()) 
	 {
        //std::string msg = sKey + " does not exist";
		return -1;
        //DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetProperty key[%s] failed.Exception[%s]",sKey.c_str(),msg.c_str());
    }
    sValue = properties->at(sKey);

	
	return 0;
}

//自动识别字符串中的key，并取得value替换后返回
int DCParseXml::ReplaceProperties(const string sOri, string &sDes)
{
	if(ReplaceProperties(m_strConfig, sOri, sDes))
	{
		return -1;
	}
	return 0;
}

//自动识别字符串中的key，并取得value替换后返回
int DCParseXml::ReplaceProperties(std::map<std::string,std::string> *properties,const string sOri, string &sDes)
{
	//DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "ReplaceProperties sOri[%s]",sOri.c_str());
	if(sOri == "")
	{
		sDes = sOri;
		return 0;
	}

	string sDestmp = sOri;
	size_t apos = 0;
	//size_t bpos = 0;
	size_t cpos = 0;
	
	string sKey = "";
	string sValue = "";
	do
	{
		//获得key名关键字，格式：      	$$key$$
		apos = sDestmp.find("$$",0);
		if(apos == string::npos)
		{
			break;
		}	
		cpos = sDestmp.find("$$",apos+2);
		if(cpos == string::npos || cpos-apos <= 2)
		{
			break;
		}		
			
		sKey = string("/") + sDestmp.substr(apos+2,cpos-apos-2);//map中KEY前面都加了反斜杠/
		sValue = "";
		if(GetProperties(properties, sKey, sValue))
		{
			sDes = sDestmp;
			//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "ReplaceProperties failed, key[%s] or Namespace is invalid.",sKey.c_str());
			return -1;
		}
		sDes = sDestmp.substr(0,apos) + sValue + sDestmp.substr(cpos+2);
		sDestmp = sDes;
	}
	while(1);
	
	sDes = sDestmp;	
	return 0;
}

int DCParseXml::Init(const char* pModuleName, const char* pConfig)
{
    const char* szPFileName = pConfig;    
	
	m_strConfig = NULL;

	int nRet = 0;

	if(szPFileName != NULL && pModuleName != NULL)//
	{
		m_strConfig_New.clear();
		m_strConfig_Old.clear();
		m_vModParam.clear();
	
		strncpy(m_szFilePath, szPFileName, MAXFILEPATHSIZE);
    	strncpy(m_szModuleName, pModuleName, MAXMODULESIZE);
	
		nRet = ReadXml(m_strConfig_New);
		if (nRet < 0)
		{
	    	return nRet;
		}
	}	

	std::map<std::string,std::string> m_strConfig_tmp;
	m_strConfig_tmp.clear();
	m_strConfig_tmp = DCPropertiesManer::Instance()->DCPropertiesBak();

	m_vsFilename.clear();
	m_vsFilename.assign(DCPropertiesManer::Instance()->DCPropertiesFile().begin(),DCPropertiesManer::Instance()->DCPropertiesFile().end());

	if(m_strConfig_New.empty() && szPFileName == NULL && pModuleName == NULL)//DFM调用初始化
	{
		m_strConfig_New = m_strConfig_tmp;
	}
	else if(szPFileName != NULL && pModuleName != NULL)
	{
		string strOriParam = "";
		string strDesParam = "";
		map<string, string>::iterator itCfg;
		map<string, string>::iterator itTmp;
		map<string, string>::iterator itFind;
		m_strConfig_repl.clear();
		for(itCfg=m_strConfig_New.begin(); itCfg != m_strConfig_New.end();itCfg++)
		{
			if(string::npos != itCfg->second.find("$$"))
			{
				m_strConfig_repl[itCfg->second]=itCfg->first;//存放xml中需要配置中心替换的$$KEY$$
				strOriParam = itCfg->second;
				DCPropertiesManer::Instance()->ReplaceProperties(strOriParam, strDesParam);
				itCfg->second = strDesParam;
			}
		}	

		for(itTmp=m_strConfig_tmp.begin(); itTmp != m_strConfig_tmp.end();itTmp++)
		{
			itFind = m_strConfig_New.find(itTmp->first);
			if(itFind == m_strConfig_New.end())
			{
				m_strConfig_New.insert(*itTmp);
			}
		}
	}
	
	m_strConfig_Old = m_strConfig_New;
	m_strConfig = &m_strConfig_New;

	//启动参数刷新线程
	if(!m_isInit && szPFileName != NULL && pModuleName != NULL)//非DFM调用初始化
	{
		int nRet = pthread_create(&s_tno, NULL, Deal, this);
		if(nRet < 0)
		{
			//DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Create Thread Failed.");
			return -1;
		}
		m_isInit = true;
	}	
	
	return 0;
}

void DCParseXml::routine()
{
	int ret = 0;
	long mtime_old = time(NULL);
	long mtime_new = time(NULL);
	struct stat newStat;

	while(m_runState)
	{
		//sleep(5);
		for(int i=0; i<50; ++i)
		{
			usleep(100*1000);
			pthread_testcancel();
		}
		
		ret = stat(m_szFilePath, &newStat);
		if(!ret && newStat.st_mtime > mtime_new)
		{
			mtime_new = newStat.st_mtime;
		}

		for(int i = 0; i < m_vsFilename.size(); i++)
		{
			ret = stat(m_vsFilename[i].c_str(), &newStat);
			if(ret != 0)
			{
				//cout<<"error: no finded config file["<<m_configPath<<"]"<<endl;
				continue;
			}
			if(newStat.st_mtime > mtime_new)
			{
				mtime_new = newStat.st_mtime;
			}
		}

		if(mtime_new > mtime_old)
		{
			mtime_old = mtime_new;
			RefreshParam();
		}
	}
}

int DCParseXml::Destroy()
{
	delete this;
	return 0;
}

int DCParseXml::ReadXml(std::map<std::string,std::string> & mapConfig)
{
    TiXmlDocument *myDocument = NULL;
	myDocument= new TiXmlDocument(m_szFilePath);
	if (!myDocument)
	{
		return -1;
	}
	myDocument->LoadFile();
	if (myDocument->Error())
	{	
		//cout<<"Error: "<<myDocument->Value()<<" "<<myDocument->ErrorDesc()<<" the row:"<<myDocument->ErrorRow()<<"."<<endl;
		delete myDocument;
		myDocument = NULL;
		//cout<<"ReadXml Error!!!"<<endl;
		return -1;
	}
	int ret= 0;
    //获得根元素
    TiXmlElement *RootElement  = myDocument->RootElement();
	TiXmlElement *pNode = NULL;
	
	//加载全部参数
	if(!strcmp(m_szModuleName, "ALL"))
	{
		for(pNode = RootElement->FirstChildElement(); pNode; pNode = pNode->NextSiblingElement())
		{
			ret = ParseNode(pNode, "", mapConfig);
			if(ret < 0)
			{
				delete myDocument;
				//printf("ReadXml ALL param Error!!!");
				return ret;
			}
		}
	}
	else
	{
		//查找公共参数
		pNode = RootElement->FirstChildElement("Common");
		if(pNode)
		{
			ret = ParseNode(pNode, "", mapConfig);
			if(ret < 0)
			{
				delete myDocument;
				//printf("ReadXml Common param Error!!!",m_szModuleName);
				return ret;
			}
		}
		//查找模块参数
		pNode = RootElement->FirstChildElement(m_szModuleName);
		if(pNode)
		{
			ret = ParseNode(pNode, "", mapConfig);
			if(ret < 0)
			{
				delete myDocument;
				//printf("ReadXml %s param Error!!!",m_szModuleName);
				return ret;
			}
		}
	}
    delete myDocument;
    return 0;

}

int DCParseXml::ParseNode(TiXmlElement *pNode, const std::string & strParentPath, std::map<std::string,std::string> & mapConfig)
{
	if(!pNode)
	{
		return 0;
	}
	int ret = 0;
	const char* child_name = NULL;
	std::string tmp;
	std::string children="^";
	std::string nPath = strParentPath + std::string("/") + pNode->Value();
	for(TiXmlElement * pChild = pNode->FirstChildElement(); 
		pChild; pChild = pChild->NextSiblingElement())
	{
		if(strcmp(pChild->Value(), "param") != 0)
		{
			if((ret = ParseNode(pChild, nPath, mapConfig)) < 0)
			{
				return ret;
			}
			child_name = pChild->Value();
		}
		else
		{
			const char * pname = pChild->AttributeParse("name");
			if(!pname)
			{
				continue;
			}
			
			child_name = pname;
			
			std::pair<std::string, std::string> elem;
			elem.first = nPath + std::string("/") + pname;
			elem.second = "";
			if(pChild->GetText())
			{
				elem.second = pChild->GetText();
			}
			

			mapConfig.insert(elem);
			
			const char * pmode = pChild->AttributeParse("mode");
			if(pmode && atoi(pmode)==1)
			{
				m_vModParam.push_back(elem.first);
			}
		}
		tmp = std::string("^")+child_name+std::string("^");
		if( children.find(tmp) == std::string::npos)
		{
			children.append(tmp.c_str()+1);
		}
	}
	if(children.size() > 1)
	{
		std::pair<std::string, std::string> elem;
		elem.first = nPath + std::string("/.");
		elem.second = children.substr(1, children.size()-2);
		mapConfig[elem.first]=elem.second;
	}
	return 0;
}

const char * DCParseXml::GetParam(const char *pName, const char *pPath)
{
	if(!m_strConfig)
	{
		return NULL;
	}

	map<string,string> * pmap = m_strConfig;
	string strFullPath = string("/") + pName;

	if(pPath && pPath!="")
	{
		strFullPath = string("/") + pPath + string("/") + pName;
	}
			
	map<string,string>::iterator it = pmap->find(strFullPath);
	if(it != pmap->end())
	{
		return it->second.c_str();
	}
	return "";
}

const char * DCParseXml::GetChild(const char *pPath, std::vector<std::string>* child)
{
	if(child)child->clear();
	const char* list = GetParam(".", pPath);
	if(!list || !list[0])
	{
		return "";
	}
	if(child)
	{
		const char* nb = list;
		const char* np = strchr(nb, '^');
		while(*nb)
		{
			if(np)
			{
				child->push_back(std::string(nb, np-nb));
				nb = np+1;
				np = strchr(nb, '^');
			}
			else
			{
				child->push_back(std::string(nb));
				break;
			}
		}
	}
	return list;
}

int DCParseXml::GetChild(const char *pPath, std::map<std::string, std::string>& params)
{
	const char* p = NULL;
	std::vector<std::string> vec;
	map<string,string>::iterator it;
	map<string,string> * pmap = NULL;
	string strFullPath;
	
	params.clear();
	p = GetChild(pPath, &vec);
	if(!p[0])
	{
		return -1;
	}
	
	pmap = m_strConfig;
	for(unsigned int i = 0; i < vec.size(); i++)
	{
		strFullPath = string("/") + pPath + string("/") + vec[i];
		it = pmap->find(strFullPath);
		if(it != pmap->end())
		{
			params.insert(make_pair(vec[i], it->second));
		}
	}
	return 0;
}

int DCParseXml::RefreshParam()
{
	map<string, string> * pmap = NULL;
	if(m_strConfig == &m_strConfig_New)
	{
		pmap = &m_strConfig_Old;
	}
	else
	{
		pmap = &m_strConfig_New;
	}
	
	if(m_strConfig && !m_vModParam.empty())
	{
		TiXmlDocument doc(m_szFilePath);
		doc.LoadFile();
		if (doc.Error())
		{	
			//printf("DCParseXml::RefreshParam, load file[%s] failed", m_szFilePath);
			return -1;
		}
		
	    //获得根元素
	    TiXmlElement *RootElement  = doc.RootElement();

		bool bfind = false;
	    //查找刷新参数
	    TiXmlElement *pNode = NULL;
		TiXmlElement *pParam = NULL;
		char szbuf[512];
		for(vector<string>::iterator iter = m_vModParam.begin();
			iter != m_vModParam.end();
			iter++)
		{
			pNode = RootElement;
			strcpy(szbuf, iter->c_str());
			const char * pName = strrchr(szbuf, '/') + 1;
			const char * pch = strtok(szbuf,"/");
			while(pch != pName && pNode)
			{
				pNode = pNode->FirstChildElement(pch);
				pch = strtok(NULL,"/");
			}
			if(pNode)
			{
				for(pParam = pNode->FirstChildElement("param");
					pParam; pParam = pParam->NextSiblingElement("param"))
				{
					if(!strcmp(pParam->AttributeParse("name"), pName))
					{
						if(atoi(pParam->AttributeParse("mode")) == 1)
						{
							//更新此参数
							string Text = pParam->GetText();
							map<string, string>::iterator oit = pmap->find(*iter);
							//if(oit->second != Text)//避免new刷了old没刷就又改配置导致配置无法生效
							{
								oit->second=Text;
								bfind = true;
							}
						}
						break;
					}
				}
			}
		}
		//if(bfind)
		//{
		//	m_strConfig = pmap;
		//}
	}
	
	std::map<std::string,std::string> m_strConfig_tmp;
	m_strConfig_tmp.clear();
	m_strConfig_tmp = DCPropertiesManer::Instance()->DCPropertiesBak();
	
	string strOriParam = "";
	string strDesParam = "";
	map<string, string>::iterator itCfg;
	map<string, string>::iterator itTmp;
	map<string, string>::iterator itFind;
	for(itCfg=pmap->begin(); itCfg != pmap->end();itCfg++)
	{
		if(string::npos != itCfg->second.find("$$"))
		{
			strOriParam = itCfg->second;
			DCPropertiesManer::Instance()->ReplaceProperties(strOriParam, strDesParam);
			itCfg->second = strDesParam;
		}			
	}

	for(itTmp=m_strConfig_tmp.begin(); itTmp != m_strConfig_tmp.end();itTmp++)
	{
		itFind = pmap->find(itTmp->first);
		if(itFind == pmap->end())
		{
			pmap->insert(*itTmp);
		}
		string strKeyTmp = itTmp->first.substr(1);
		strKeyTmp = string("$$") + strKeyTmp + string("$$");
		itFind = m_strConfig_repl.find(strKeyTmp);
		if(itFind != m_strConfig_repl.end())//XML中有需要替换的$$KEY$$
		{
			(*pmap)[itTmp->first]=itTmp->second;//把对应配置中心的值插入map
		}
	}
	m_strConfig = pmap;
		
	return 0;
}
