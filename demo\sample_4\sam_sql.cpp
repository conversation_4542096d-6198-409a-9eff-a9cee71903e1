#include "DCDBManer.h"
#include "DCLogMacro.h"
#include <stdio.h>
#include <iostream>
using namespace std;

int test_fee_sql(DCDBManer& dbm, const char* name);
int test_rat_sql(DCDBManer& dbm, const char* name);
int test_dca_sql(DCDBManer& dbm, const char* name,string s1,string s2);

int main(int argc, char*argv[])
{
	if(argc < 2)
	{
		printf("Usage: %s sql.xml\n", argv[0]);
		return 1;
	}
	int ret = 0;
	DCDBManer dbm;

/*	ret = DCLOGINIT("ocs", "sam4", 7, "/public/ocs_ah/build/tools/dfmsample");
	if(ret < 0)
	{
		printf("init log failed\n");
		return 1;
	}
	printf("init log success\n");
*/
	ret = dbm.Init(argv[1]);
	if(ret < 0)
	{
		printf("init dbm failed\n");
        return ret;
	}
	printf("init dbm success\n");

	test_dca_sql(dbm, "q_tb_prd_prd_inst_accarea",argv[2],argv[3]);

	dbm.DelSQL("q_tb_prd_prd_inst_accarea");

	//sleep(3);
	return 0;
}

/*int test_fee_sql(DCDBManer& dbm, const char* name)
{
	UDBSQL* qFee = dbm.GetSQL(name);
    if(!qFee)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "fee", "GetSQL for %s failed", name);
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 1, "fee","GetSQL for %s success", name);

	long acct_item_type_id = 0;
	long duration = 0;
	long charge = 0;
	try
	{
		qFee->UnBindParam();
		qFee->BindParam(1, 1001L);
		qFee->Execute();

		if(qFee->Next())
		{
			qFee->GetValue(1, acct_item_type_id);
			qFee->GetValue(2, duration);
			qFee->GetValue(3, charge);

			DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "fee", "acct_item_type_id[%ld], duration[%ld], charge[%ld]", \
						acct_item_type_id, duration, charge);
		}
	}
	catch(UDBException&e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "fee", "exp:%s", e.what());
		return -1;
	}
	return 0;
}

int test_rat_sql(DCDBManer& dbm, const char* name)
{
	UDBSQL* qRat = dbm.GetSQL(name);
    if(!qRat)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "rat", "GetSQL for %s failed", name);
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DVIEW, 1, "rat","GetSQL for %s success", name);

	long prd_inst_id=204563440L;
	long ratable_id = 0;
	long bill_cycle_id = 0;
	char service_nbr[32]={0};
	try
	{
		qRat->UnBindParam();
		qRat->BindParam(1, prd_inst_id);
		qRat->Execute();

		if(qRat->Next())
		{
			qRat->GetValue(1, ratable_id);
			qRat->GetValue(2, bill_cycle_id);
			qRat->GetValue(3, service_nbr);

			DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "rat", "ratable_id[%ld], bill_cycle_id[%ld], service_nbr[%s]", \
						ratable_id, bill_cycle_id, service_nbr);
		}
	}
	catch(UDBException&e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "rat", "exp:%s", e.what());
		return -1;
	}
	return 0;
}*/
int test_dca_sql(DCDBManer& dbm, const char* name,string s1,string s2)
{
	UDBSQL* qDca = dbm.GetSQL(name);
    if(!qDca)
	{
		cout<<"GetSQL for "<<name<<" failed"<<endl;
		return -1;
	}

	cout<<"GetSQL for "<<name<<" success"<<endl;


	char value[32]={0};
	cout<<"phone: "<<s1<<"  area: "<<s2<<endl;
	try
	{
		qDca->UnBindParam();
		qDca->BindParam(1, s1);
		qDca->BindParam(2, s2);
		qDca->Execute();

		if(qDca->Next())
		{
			qDca->GetValue(1, value);
			cout<<"prd_inst_id:  "<<value<<endl;
			qDca->GetValue(2, value);
			cout<<"latn id:  "<<value<<endl;
			
		}
		else
			cout<<"no data"<<endl;
	}
	catch(UDBException&e)
	{
		cout<<"exp: "<<e.what()<<endl;
		return -1;
	}
	
	return 0;
}