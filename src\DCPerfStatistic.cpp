
#include "DCPerfStatistic.h"
#include <stdio.h>
#include <string.h>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <algorithm>
#include <unistd.h>
#include <sys/time.h>
#include <pthread.h>
#include <time.h>
#include "DCLogMacro.h"
#include <string.h>
#include <stdio.h>

// 静态成员变量
static pthread_mutex_t s_mutex;
static pthread_mutexattr_t s_mutexAttr;
static int s_instanceCount = 0; // 引用锁销毁计数器

struct DCPerfGuard
{
	pthread_mutex_t* m_mutex;
	DCPerfGuard(pthread_mutex_t* mutex)
		: m_mutex(mutex)
	{
		pthread_mutex_lock(m_mutex);
	}
	~DCPerfGuard()
	{
		pthread_mutex_unlock(m_mutex);
	}
};


static int s_name_ds = 0;


struct DCPerfStatsAttr
{
	long CreateTimestamp;
	bool Breset;

	DCPerfStatsAttr() :
		CreateTimestamp(long(0)),
		Breset(bool(false))
        { }
	
};

static long s_interval = 0; //时间间隔
static std::map<std::string, DCPerfStatsAttr*> s_statsAttr; //性能指标属性


namespace func_statistic
{
	void GetHostAddrIp(char* pszAddrIp)
	{
		char hname[128] = { 0 };
		struct hostent *hent;
		int i;

		if (gethostname(hname, sizeof(hname)))
		{
			fprintf(stderr, "gethostname executed failed.");
			return;
		}
		hent = gethostbyname(hname);
		if (NULL == hent)
		{
			fprintf(stderr, "gethostbyname executed failed.");
			return;
		}

		if (hent->h_addr_list[0])
		{
			sprintf(pszAddrIp, "%s", inet_ntoa(*(struct in_addr*)(hent->h_addr_list[0])));
		}
	}
}


DCPerfStatistic::DCPerfStatistic(std::string strIdentify)
{
	// 确保递归锁只初始化一次
	static pthread_once_t once_control = PTHREAD_ONCE_INIT;
	
	pthread_once(&once_control, []() {
		// 初始化递归锁
		pthread_mutexattr_init(&s_mutexAttr);
		pthread_mutexattr_settype(&s_mutexAttr, PTHREAD_MUTEX_RECURSIVE);
		pthread_mutex_init(&s_mutex, &s_mutexAttr);
	});
	__sync_add_and_fetch(&s_instanceCount, 1); // 每次累加计数1

	// 使用递归锁保护初始化过程
	DCPerfGuard guard(&s_mutex);

	std::pair<std::string, DCPerfTimeStats*> val;
	s_name_ds = (int)((char*)&val.second - (char*)&val.first);

	m_strIdentify = strIdentify;

	memset(m_szLocalHostid, 0x00, sizeof(m_szLocalHostid));
	func_statistic::GetHostAddrIp(m_szLocalHostid);

	memset(m_szCluster, 0x00, sizeof(m_szCluster));
	char *KpiCluster = getenv("KPI_CLUSTER");  //获取环境变量的集群名称
	if (KpiCluster != NULL)
	{
	    sprintf(m_szCluster,"%s",KpiCluster);
	}

    s_interval = 10000;  //默认10秒
	char *PerfLogInterval = getenv("PERF_LOG_INTERVAL");  //获取环境变量的时间间隔
	if (PerfLogInterval != NULL)
	{
	    s_interval = atoi(PerfLogInterval);
	}
	
}

DCPerfStatistic::~DCPerfStatistic()
{
	clear();
	if (__sync_sub_and_fetch(&s_instanceCount, 1) == 0) // 减少计数，判断为0时才做锁销毁
	{ 
		pthread_mutexattr_destroy(&s_mutexAttr);
		pthread_mutex_destroy(&s_mutex);
	}
}

void DCPerfStatistic::clear()
{
    DCPerfGuard guard(&s_mutex);
    for(std::map<std::string, DCPerfStatsAttr*>::iterator Attriter = s_statsAttr.begin(); Attriter != s_statsAttr.end(); Attriter++)
	{
		if (Attriter->second != NULL)
		{
		    delete Attriter->second;
		}
		
	}
	s_statsAttr.clear();

	for(std::map<std::string, DCPerfTimeStats*>::iterator it = m_stats.begin(); it != m_stats.end(); it++)
	{
		if (it->second != NULL)
		{
		    delete it->second;
		}
		
	}
	m_stats.clear();
}

void DCPerfStatistic::reset()
{
    DCPerfGuard guard(&s_mutex);
	std::map<std::string, DCPerfStatsAttr*>::iterator Attriter = s_statsAttr.find(m_strIdentify);
	if(Attriter != s_statsAttr.end())
	{

	     if(Attriter->second->Breset)
		 {
				for(std::map<std::string, DCPerfTimeStats*>::iterator iter = m_stats.begin();
				iter != m_stats.end(); iter++)
				{
					__sync_sub_and_fetch(&(iter->second->max_us), iter->second->max_us);
					__sync_sub_and_fetch(&(iter->second->min_us), iter->second->min_us);

					__sync_sub_and_fetch(&(iter->second->s_nr),   iter->second->s_nr);
					__sync_sub_and_fetch(&(iter->second->s_us),   iter->second->s_us);
					__sync_sub_and_fetch(&(iter->second->lst_us), iter->second->lst_us);
				}

				Attriter->second->Breset = false;
		 }
	
	}

}

DCPerfTimeStats* DCPerfStatistic::get_position(const char* stat_name)
{
    DCPerfGuard guard(&s_mutex);
    std::map<std::string, DCPerfStatsAttr*>::iterator Attriter = s_statsAttr.find(m_strIdentify);
	if(Attriter == s_statsAttr.end())
	{
	     DCPerfStatsAttr* Ap = new DCPerfStatsAttr();
	     memset(Ap, 0, sizeof(DCPerfStatsAttr));
		 struct timeval tmv;
		 gettimeofday(&tmv, NULL);
		 Ap->CreateTimestamp = tmv.tv_sec*1000 + tmv.tv_usec/1000; 
	     s_statsAttr[std::string(m_strIdentify)] = Ap;
	}

	std::map<std::string, DCPerfTimeStats*>::iterator iter=m_stats.find(stat_name);
	if(iter != m_stats.end())
	{
		return iter->second;
	}
	DCPerfTimeStats* p = new DCPerfTimeStats();
	memset(p, 0, sizeof(DCPerfTimeStats));
	m_stats[std::string(stat_name)] = p;
	return p;
}

void DCPerfStatistic::del_position(const char* stat_name)
{
    DCPerfGuard guard(&s_mutex);
    std::map<std::string, DCPerfStatsAttr*>::iterator Attriter = s_statsAttr.find(m_strIdentify);
	if(Attriter != s_statsAttr.end())
	{
		if (Attriter->second != NULL)
		{
		    delete Attriter->second;
		}
		s_statsAttr.erase(Attriter);
	}
    
	std::map<std::string, DCPerfTimeStats*>::iterator iter=m_stats.find(stat_name);
	if(iter != m_stats.end())
	{
		if (iter->second != NULL)
		{
		    delete iter->second;
		}	
		m_stats.erase(iter);
	}
}

void DCPerfStatistic::set_time(DCPerfTimeStats*pos, long usec, bool nr)
{
	if(!pos)
		return;

	DCPerfTimeStats& sa = *pos;
	if(sa.tot_us + usec < sa.tot_us)
	{
		//安全起见， tot_us 重置则 tot_nr 重置
		__sync_sub_and_fetch(&sa.tot_nr, sa.tot_nr);
		__sync_sub_and_fetch(&sa.tot_us, sa.tot_us);
		sa.max_us = 0;
		sa.min_us = 0;
	}

	if (nr)
	{
		if (sa.s_nr >= 2)
		{
			if (sa.lst_us > sa.max_us)
			{
				sa.max_us = sa.lst_us;
			}

			if (sa.lst_us < sa.min_us)
			{
				sa.min_us = sa.lst_us;
			}
		}
	
		__sync_add_and_fetch(&sa.s_nr,   1);
		__sync_add_and_fetch(&sa.tot_nr, 1);
		__sync_sub_and_fetch(&sa.lst_us, sa.lst_us);
	}

	__sync_add_and_fetch(&sa.s_us,   usec);
	__sync_add_and_fetch(&sa.tot_us, usec);
	__sync_add_and_fetch(&sa.lst_us, usec);

	if (sa.s_nr == 1)
	{
		sa.max_us += usec;
		sa.min_us += usec;
	}
}

void DCPerfStatistic::to_string(std::string& out, bool filter)
{
	char buf[128];
	for(std::map<std::string, DCPerfTimeStats*>::iterator it = m_stats.begin();
		it != m_stats.end(); it++)
	{
		if(!filter || it->second->s_nr)
		{
#ifndef _DFM_DEBUG_
			if(it->second->s_us < 2000)
			{
				;
			}
			else
#endif//#ifndef _DFM_DEBUG_
			{	
				sprintf(buf, "[%s=%lu|%lu]", it->first.c_str(), it->second->s_nr, it->second->s_us);
				out.append(buf);
			}
		}
	}
}

void DCPerfStatistic::to_json_string(std::string& sOut, bool filter, bool bSample)
{
    DCPerfGuard guard(&s_mutex);
	std::map<std::string, DCPerfStatsAttr*>::iterator Attriter = s_statsAttr.find(m_strIdentify);
	if(Attriter != s_statsAttr.end())
	{
	     struct timeval tmv;
		 gettimeofday(&tmv, NULL);
		 long now_tm = tmv.tv_sec*1000+tmv.tv_usec/1000;

	     if(now_tm > Attriter->second->CreateTimestamp + s_interval)
		 {
  
                Attriter->second->CreateTimestamp = now_tm;
				Attriter->second->Breset = true;
		 }
		 else
		 {
		     return;
		 }
	
	}

	char buf[256] = { 0 };
	std::string strKeyWithYinhao = "\"" + m_strIdentify + "\"";
	
	for(std::map<std::string, DCPerfTimeStats*>::iterator it = m_stats.begin();	it != m_stats.end(); it++)
	{
		if(!filter || it->second->s_nr)
		{
			std::string strKeySub = "";
			if (bSample)
				strKeySub = "\"" + it->first + "^";
			else
				strKeySub = "\"" + it->first + "\"";

#ifndef _DFM_DEBUG_
			if(it->second->s_us < 2000)
			{
				;
			}
			else 
#endif//#ifndef _DFM_DEBUG_
			if (sOut.find(strKeySub.c_str()) != std::string::npos)
			{
				;
			}
			else
			{
				memset(buf, 0x00, sizeof buf);

				//性能日志无法解析'|'(竖线)，需要替换为'(单引号)
				std::string bfstr = "|";
				std::string afstr = "\'";
				std::string str = it->first;
				size_t pos = str.find(bfstr);
				while(pos != std::string::npos)
				{
					str.replace(pos, 1, afstr);
					pos = str.find(bfstr, pos+1);
				}

				//性能日志无法解析'_'(下划线)，需要替换为无
				std::string bfstr2 = "_";
				std::string afstr2 = "";
				size_t pos2 = str.find(bfstr2);
				while(pos2 != std::string::npos)
				{
					str.replace(pos2, 1, afstr2);
					pos2 = str.find(bfstr2, pos2);
				}

				if (bSample)
				{
					/*
					{
					"APP":[
					"USR.T^1^300^300^300",
					"USR.Fun1^2^50^30^20"
					],
					"DCA":[
					"BILL.T^1^700^700^700",
					"PLCA.T^2^1400^800^600"
					],
					"SVR":[
					"BILL.T^5^1000^250^50",
					"BILL.919^2^450^250^200",
					"BILL.910^3^550^200^50"
					]
					}
					*/
				
					sprintf(buf, "\"%s^%lu^%lu^%lu^%lu\"", 
						str.c_str(), it->second->s_nr, it->second->s_us, it->second->max_us, it->second->min_us);
				}
				else
				{
					/*
					{
					"APP":[
					{"USR.T":{"CNT":1,"T":300,"Max":300,"Min":300}},
					{"USR.Fun1":{"CNT":2,"T":50,"Max":30,"Min":20}}
					],
					"DCA":[
					{"BILL.T":{"CNT":1,"T":700,"Max":700,"Min":700}},
					{"PLCA.T":{"CNT":2,"T":1400,"Max":800,"Min":600}}
					],
					"DMDB":[
					{"BILL.T":{"CNT":1,"T":1000,"Max":1000,"Min":1000}},
					{"PLCA.T":{"CNT":2,"T":1400,"Max":800,"Min":600}}
					],
					"MYSQL":[
					{"BILL.T":{"CNT":1,"T":2000,"Max":2000,"Min":2000}},
					{"PLCA.T":{"CNT":2,"T":3400,"Max":1800,"Min":1600}}
					],
					"DCF":[
					{"BILL.T":{"CNT":1,"T":100,"Max":100,"Min":100}},
					{"PLCA.T":{"CNT":2,"T":300,"Max":160,"Min":140}}
					],
					"SVR":[
					{"BILL.T":{"CNT":5,"T":1000,"Max":250,"Min":50}},
					{"BILL.919":{"CNT":2,"T":450,"Max":250,"Min":200}},
					{"BILL.910":{"CNT":3,"T":550,"Max":200,"Min":50}}
					]
					}
					*/
					
					if (it->second->lst_us > it->second->max_us)
					{
						it->second->max_us = it->second->lst_us;
					}

					if (it->second->lst_us < it->second->min_us)
					{
						it->second->min_us = it->second->lst_us;
					}
					sprintf(buf, "{\"%s\":{\"CNT\":%lu,\"T\":%lu,\"Max\":%lu,\"Min\":%lu}}", 
						str.c_str(), it->second->s_nr, it->second->s_us, it->second->max_us, it->second->min_us);
				}

				
				if (sOut.empty())
				{
					sOut.assign(m_szLocalHostid);
					sOut.append("|");
					sOut.append(m_szCluster);
					sOut.append("|");
					if (bSample) sOut.append("1|{"); else sOut.append("0|{");
					sOut.append(strKeyWithYinhao);
					sOut.append(":[]}");
				}

				size_t idx_id = sOut.find(strKeyWithYinhao);
				if (idx_id == std::string::npos)
				{
					size_t idx_insert_pos = sOut.length() - 1;
					
					if (sOut.at(idx_insert_pos - 1) != '{')
					{   
						sOut.insert(idx_insert_pos, 1, ',');
						idx_insert_pos += 1;
					}

					std::string identify_value = strKeyWithYinhao + ":[]";
					sOut.insert(idx_insert_pos, identify_value);

					idx_id = sOut.find(strKeyWithYinhao);
				}

				size_t idx_insert_pos1 = idx_id + strKeyWithYinhao.length() + 2;
				while (sOut.at(idx_insert_pos1) != ']' && idx_insert_pos1 < sOut.length())
				{
                      ++idx_insert_pos1;
				};

				if (sOut.at(idx_insert_pos1 - 1) != '[')
				{
					sOut.insert(idx_insert_pos1, 1, ',');
					idx_insert_pos1 += 1;
				}

				sOut.insert(idx_insert_pos1, buf);
			}
		}
	}
}

