/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "cpx.hh"
#include <stdio.h>
#include "DCSeriaOp.h"

int
main()
{
	DCSeriaEncoder en(ESeriaBinary);
	DCSeriaDecoder de(ESeriaBinary);

	c::cpx c1;
	for(int i=0; i<260; i++)
	{
		c1.re = i;
    	c1.im = 0.13 + i;
    	en.encode(c1);
	}

	printf("0en size[%lu]\n", en.size());

	de.set(en.data(), en.size());
	for(int i=0; i<260; i++)
	{
    	de.decode(c1);
		printf("0(%f, %f)\n", c1.re, c1.im);
	}

	en.clear();
	for(int i=0; i<260; i++)
	{
		c1.re = i;
    	c1.im = 0.13 + i;
    	en.encode(c1);
	}

	printf("1en size[%lu]\n", en.size());

	de.set(en.data(), en.size());
	for(int i=0; i<260; i++)
	{
    	de.decode(c1);
		printf("1(%f, %f)\n", c1.re,c1.im);
	}
    return 0;
}

void DCLogOut(int CLASS, int LEVEL, int err, const char* file, int line, const char* func, const char* key, const char* fmt, ...)
{

}