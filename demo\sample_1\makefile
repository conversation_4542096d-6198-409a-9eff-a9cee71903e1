
CC=g++ -m64
CFLAGS=-g -Wall -fPIC
DFLAGS=-g -Wall -shared
LFLAGS=-g -Wall

FM=/public/tsf/ocs/dfm

sample=$(FM)/demo/sample_1

inc=-I$(FM)/include

version=1.0.0

libtarget=libfc_add.so.$(version) libfc_multi.so.$(version) libfl_algo.so.$(version) libflow_algo.so.$(version)

bintarget=SamAlgo

.PHONY:all clean

all: $(libtarget) $(bintarget)

libfc_add.so.$(version):SamAdd.o
	$(CC) $(DFLAGS) -o $@ $^
	
libfc_multi.so.$(version):SamMulti.o
	$(CC) $(DFLAGS) -o $@ $^
	
libfl_algo.so.$(version):SamAlgoOne.o
	$(CC) $(DFLAGS) -o $@ $^

libflow_algo.so.$(version):SamAlgoFlow.o
	$(CC) $(DFLAGS) -o $@ $^
	
$(bintarget):SamAlgo.o
	$(CC) $(LFLAGS) -o $@ $^ -L$(FM)/lib -ldfm
	
%.o:%.cpp
	$(CC) $(CFLAGS) -c $< -I$(FM)/include
	
clean:
	-rm -f $(libtarget) $(bintarget) *.o
	
