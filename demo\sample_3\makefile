
CC=g++ -m64
CFLAGS=-g -Wall -fPIC
DFLAGS=-g -Wall -shared
LFLAGS=-g -Wall

FM=/public/tsf/ocs/dfm

AVRO=/public/tsf/ocs/third/avrocpp

sample=$(FM)/demo/sample_3

inc=-I$(FM)/include

version=1.0.0

bintarget=gen3

.PHONY:all clean

all: $(bintarget)
	
$(bintarget):gen3.o
	$(CC) $(LFLAGS) -o $@ $^ -L$(FM)/lib -ldfm
	
%.o:%.cpp
	$(CC) $(CFLAGS) -c $< -I$(FM)/include -I$(sample)/include -I$(AVRO)/include

%.o:%.cc
	$(CC) $(CFLAGS) -c $< -I$(FM)/include -I$(sample)/include -I$(AVRO)/include
	
clean:
	-rm -f  $(bintarget) *.o
	
