#ifndef __DCSERIAOP_H__
#define __DCSERIAOP_H__

#include "avro/Specific.hh"
#include "avro/Encoder.hh"
#include "avro/Decoder.hh"
#include "avro/Printer.hh"
#include "avro/Stream.hh"
#include <vector>
#include <string>

enum ESeriaType
{
    ESeriaBinary = 1,		/*二进制序列化*/
	ESeriaBinString,		/*二进制串序列化,不含有0x0的字节*/
};

class DCSeriaEncoder
{
public:
	DCSeriaEncoder(ESeriaType type);

	~DCSeriaEncoder();

    template <typename T >
    void encode(T const& t)
    {
        avro::encode(*m_e, t);
		m_e->flush();
    }

    size_t size();

    const uint8_t* data();

	void clear();

private:
	avro::EncoderPtr m_e;
	std::auto_ptr<avro::OutputStream> m_out;
};

class DCSeriaDecoder
{
public:
	DCSeriaDecoder(ESeriaType type);

	~DCSeriaDecoder();

    void set(const uint8_t* data, size_t size);

    void set(const std::vector<uint8_t>& data);

    template <typename T >
    void decode(T& t)
    {
        avro::decode(*m_e, t);
    }

private:
	avro::DecoderPtr m_e;
	std::auto_ptr<avro::InputStream> m_in;
};

class DCSeriaPrinter
{
public:
	DCSeriaPrinter();

	~DCSeriaPrinter();

    template <typename T >
    void print(T const& t)
    {
        avro::print(*m_p, NULL, t);
		m_p->flush();
    }

    size_t size();

    const char* data();

	void clear();

private:
	avro::PrinterPtr m_p;
	std::auto_ptr<avro::OutputStream> m_out;
};

std::string HexEncode(const uint8_t* data, size_t size);

std::vector<uint8_t> HexDecode(const char* data, size_t size);

#endif // __DCSERIAOP_H__
