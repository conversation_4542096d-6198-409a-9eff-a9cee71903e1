#ifndef _READ_CONFIG_
#define _READ_CONFIG_

#include <map>
#include <string>
#include <vector>
#include "TThreadXml.h"
using namespace std;

#define MAXFILEPATHSIZE 256
#define MAXMODULESIZE 50
#define STR_WHITESPACE " \t\n\r\v\f"

class TiXmlElement;
class DCParseXml : public TThreadXml
{

public:
    ~DCParseXml();
    static DCParseXml* Instance();
    int Init(const char* pModuleName, const char* pConfig);

	/*
	* @pName  : 参数名
	* @pPath   : 参数路径，如: RE/system
	*/
	const char * GetParam(const char *pName, const char *pPath);
	
	const char * GetChild(const char *pPath, std::vector<std::string>* child);
	
	int GetChild(const char *pPath, std::map<std::string, std::string>& param);

	const char * GetModuleName()
	{
		return m_szModuleName;
	}
	
	int RefreshParam();
	

	//自动识别字符串中的key，并取得value替换后返回
	int ReplaceProperties(const std::string sOri, std::string &sDes);	

	
private:


	//取得key的值
	int GetProperties(std::map<std::string,std::string> *properties,const std::string sKey, std::string &sValue);

	//自动识别字符串中的key，并取得value替换后返回
	int ReplaceProperties(std::map<std::string,std::string> *properties,const std::string sOri, std::string &sDes);

	DCParseXml();
	
	int ReadXml(std::map<std::string,std::string> & mapConfig);
	int ParseNode(TiXmlElement *pNode, const std::string &strParentPath, std::map<std::string,std::string> & mapConfig);

public:
	void routine();

private:
	int Destroy();

private:
    static DCParseXml* m_pDcParseXml;
    char m_szFilePath[MAXFILEPATHSIZE];
    char m_szModuleName[MAXMODULESIZE];

    std::map<std::string,std::string> *m_strConfig;
    std::map<std::string,std::string> m_strConfig_Old;
    std::map<std::string,std::string> m_strConfig_New;
	std::vector<std::string> m_vModParam;		//需要刷新修改的参数

	bool m_isInit;
	bool m_runState;
	std::vector<std::string> m_vsFilename;//配置中心文件：公共/私有命名空间
	std::map<std::string,std::string> m_strConfig_repl;//XML中需要配置中心替换的$$KEY$$
	
};

#endif