#ifndef __DCBASEPLUGIN_H__
#define __DCBASEPLUGIN_H__

#include <vector>

#define UNSUPPORTEDFUNC -(int)0x7FFFFFFF
#define UNSUPPORTEDFLOW -(int)0x7FFFFFFE

#define DYN_PLUGIN_CREATE(className, cate, func, ver) \
extern "C" { \
DCBasePlugin* s_plugin_create() \
{ \
    return new className(cate, func, ver); \
} \
const char* mod_category = cate ; \
const char* mod_func = func ; \
const char* mod_version = ver ; \
}

class DCPluginManer;
class DCPluginManerImp;
class DCPerfTimeStats;
class DCDBManer;
class DCAClient;
class DCRFData;
class DCBasePlugin
{
	friend class DCPluginManerImp;
public:
	/* 构造函数 */
	DCBasePlugin(const char* category /* ="base" */, const char* func /* ="base" */, const char* version /* ="1.0.0" */);

	/* 析构函数 */
	virtual ~DCBasePlugin();

	/* 功能分类 */
    const char* category() const {return m_category; }

	/* 功能名称 */
    const char* func() const {return m_funcname; }

	/* 版本号 */
    const char* version() const {return m_version; }

    /* 描述简介 */
    virtual const char* desc();

	/* 插件加载初始化 */
    int initialize();

	/* 功能调用接口 */
    int call(void* input, void* output);

protected:
	/* 获取数据库管理句柄 */
	DCDBManer* dbm();

	/* 获取DCA句柄 */
	DCAClient* dca();

	/* 获取数据刷新管理句柄 */
	DCRFData* drf();

	/* 获取附加句柄 */
    void* gethandle(const char* name);

	/* 实现：插件加载初始化 */
    virtual int init() {return 0;}

	/* 实现：功能调用接口 */
    virtual int process(void* input, void* output) = 0;

	/* 实现：依次调用子功能 */
    virtual int call_all(void* input, void* output);

	/* 实现：调用特定子功能 */
    virtual int call_one(const char* func, void* input, void* output);

	/* 实现：自定义调用子功能 */
    virtual int call_custom(const char* category, const char* func, void* input, void* output){return 0; }

    /* 获取子功能插件 */
    DCBasePlugin* get_child_plugin(const char* func);

	/* 子功能集合 */
	std::vector<DCBasePlugin*> m_calls;

private:
	/* 禁止拷贝 */
	DCBasePlugin(const DCBasePlugin& );
	DCBasePlugin& operator=(const DCBasePlugin&);

private:
    char m_category[64];
    char m_funcname[64];
    char m_version[12];
    DCPerfTimeStats* m_timepos;
    DCPluginManer* m_pluginman;
};

#endif // __DCBASEPLUGIN_H__
