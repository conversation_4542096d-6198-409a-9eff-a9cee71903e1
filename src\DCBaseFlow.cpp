#include "DCBaseFlow.h"
#include "DCLogMacro.h"
#include <string.h>

DCBaseFlow::DCBaseFlow(const char* category, const char* func, const char* version)
    :DCBasePlugin(category, func, version)
{

}

DCBaseFlow::~DCBaseFlow()
{
	// 析构该流程下的所有子功能插件
	for(std::vector<DCBasePlugin*>::iterator it = m_calls.begin(); it != m_calls.end(); it++)
	{
        delete *it;
	}
	m_calls.clear();
}

const char* DCBaseFlow::desc()
{
    return "flow plugin";
}

int DCBaseFlow::call_all(void* input, void* output)
{
	STFlowItem* iflow = &m_flows.front();
    int ret = 0;
	void* mid = input;
	std::map<std::string, STFlowItem*>::iterator ntflow;
	while(iflow)
	{
		ret = iflow->start->call(mid, output);
    	if(ret == 0)
		{
			ntflow = iflow->next.find("OK");
		}
		else
		{
			ntflow = iflow->next.find("FAIL");
		}

		if(ntflow == iflow->next.end())
		{
			iflow = NULL;
		}
		else
		{
			iflow = ntflow->second;
			mid = output;
		}
	}
	return ret;
}

int DCBaseFlow::call_one(const char* func, void* input, void* output)
{
	STFlowItem* iflow = get_flowitem(func);
	if(!iflow)
	{
		DCSYSLOG(DCLOG_LEVEL_ERROR, UNSUPPORTEDFLOW, "not find child flow func plugin: %s", func);
        return UNSUPPORTEDFLOW;
	}
	return iflow->start->call(input, output);
}

DCBaseFlow::STFlowItem* DCBaseFlow::get_flowitem(const char* name)
{
	std::vector<STFlowItem>::iterator iter;
    for(iter = m_flows.begin(); iter != m_flows.end(); iter++)
	{
        if(strcmp(iter->name, name) == 0)
		{
			return &(*iter);
		}
	}
	return NULL;
}
