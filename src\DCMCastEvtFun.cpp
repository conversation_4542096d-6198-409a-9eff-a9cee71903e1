#include "DCMCastEvtFun.h"
#include "DCRFData.h"
#include "DCLogMacro.h"
#include "CircuitBreaker.h"

#include <sys/types.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

void LogEventFun::callback(const char*name, const char* value)
{
	const char* p = NULL;
	int val = atoi(value);
	
	DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "recv name[%s] value[%s]", name, value);
	
	if(strstr(name, "log.biz.") && val >= 0 && val <= 7)
	{
		DCLOG_SETLEVEL(DCLOG_CLASS_SYS,  val);
		DCLOG_SETLEVEL(DCLOG_CLASS_BIZ,  val);
	}
	else if(strstr(name, "log.perf.") && val >= 0)
	{
		DCLOG_SETLEVEL(DCLOG_CLASS_PERF,  val);
	}
	else if(strstr(name, "log.perfusec.") && val >= 0)
	{
		DCLOG_SETCTL(DCLOG_MASK_PERF,  val);
	}
	else if(strstr(name, "log.tnbr.") && val >= 0)
	{
		DCLOG_SETCTL(DCLOG_MASK_TNBR,  val);
	}
}

void LogEventFun::register_event(const char* mod)
{
	char host[80]={0};
	char name[128]={0};
	long pid = getpid();
	gethostname(host, sizeof(host));

	/// register log.biz by host
	sprintf(name, "log.biz.%s", mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
    
	sprintf(name, "%s.log.biz.%s", host, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	sprintf(name, "%s.%ld.log.biz.%s", host, pid, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	/// register log.perf by host
	sprintf(name, "log.perf.%s", mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
    
	sprintf(name, "%s.log.perf.%s", host, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	sprintf(name, "%s.%ld.log.perf.%s", host, pid, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));

       /// register log.perfusec by host
       sprintf(name, "log.perfusec.%s", mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
    
	sprintf(name, "%s.log.perfusec.%s", host, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	sprintf(name, "%s.%ld.log.perfusec.%s", host, pid, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	/// register log.tnbr by host
	sprintf(name, "log.tnbr.%s", mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
    
	sprintf(name, "%s.log.tnbr.%s", host, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	sprintf(name, "%s.%ld.log.tnbr.%s", host, pid, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
}


void BoltEventFun::callback(const char*name, const char* value)
{
	const char* p = NULL;
	int val = atoi(value);
	
	DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "recv name[%s] value[%s]", name, value);

	/*	
	if(strstr(name, "log.biz.") && val >= 0 && val <= 7)
	{
		DCLOG_SETLEVEL(DCLOG_CLASS_SYS,  val);
		DCLOG_SETLEVEL(DCLOG_CLASS_BIZ,  val);
	}
	else if(strstr(name, "log.perf.") && val >= 0)
	{
		DCLOG_SETLEVEL(DCLOG_CLASS_PERF,  val);
	}
    else if(strstr(name, "log.perfusec.") && val >= 0)
	{
		DCLOG_SETCTL(DCLOG_MASK_PERF,  val);
	}
	else if(strstr(name, "log.tnbr.") && val >= 0)
	{
		DCLOG_SETCTL(DCLOG_MASK_TNBR,  val);
	}
	else */if(strstr(name, "data.refresh.") && val > 0)
	{
		m_drf->refresh();
	}
	if ((strstr(name, "CirB.") || strstr(name, "CirBR.")) && val > 0)
	{
	    DCCircuitBreaker::GetInatance()->SetCirB(name);
	}
	
}

void BoltEventFun::register_event(const char* topology, int taskid, const char* mod)
{
	char host[80]={0};
	char name[128]={0};
	long pid = getpid();
	gethostname(host, sizeof(host));
	
	/// register log.biz by host
	/*
	sprintf(name, "%s.log.biz.%s", host, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.%ld.log.biz.%s", host, pid, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	/// register log.biz by topology
	sprintf(name, "%s.log.biz.all", topology);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.log.biz.%s", topology, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.%d.log.biz.%s", topology, taskid, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	/// register log.perf by host
	sprintf(name, "%s.log.perf.%s", host, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.%ld.log.perf.%s", host, pid, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	/// register log.perf by topology
	sprintf(name, "%s.log.perf.all", topology);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.log.perf.%s", topology, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.%d.log.perf.%s", topology, taskid, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));

    /// register log.perfusec by host
	sprintf(name, "%s.log.perfusec.%s", host, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	sprintf(name, "%s.%ld.log.perfusec.%s", host, pid, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));

    /// register log.perfusec by topology
	sprintf(name, "%s.log.perfusec.all", topology);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.log.perfusec.%s", topology, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.%d.log.perfusec.%s", topology, taskid, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	/// register log.tnbr by host
	sprintf(name, "%s.log.tnbr.%s", host, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));
	
	sprintf(name, "%s.%ld.log.tnbr.%s", host, pid, mod);
	m_mcm->bind_event(name, new LogEventFun(m_mcm));

    /// register log.tnbr by topology
	sprintf(name, "%s.log.tnbr.all", topology);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.log.tnbr.%s", topology, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	
	sprintf(name, "%s.%d.log.tnbr.%s", topology, taskid, mod);
	m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	*/
	
	/// register data.refresh by topology
	if(m_drf)
	{
		sprintf(name, "data.refresh.all");
		m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
		
		sprintf(name, "data.refresh.%s", mod);
		m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));

		sprintf(name, "data.refresh.%s.%s", mod, host);
		m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));

		sprintf(name, "data.refresh.%s.%s.%ld", mod, host, pid);
		m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));
	}
	else if(strstr(topology, "CirB|CirBR")) //register CirB by topology
	{
	    //-------------------CircuitBreaker---------------------
	    sprintf(name, "CirB");
		m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));


		//-------------------CircuitBreakerRecover---------------------
	    sprintf(name, "CirBR");
		m_mcm->bind_event(name, new BoltEventFun(m_mcm, m_drf));

	}
}
