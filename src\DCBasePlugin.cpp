#include "DCBasePlugin.h"
#include "DCPerfStatistic.h"
#include "DCPluginManer.h"
#include "DCLogMacro.h"
#include <string.h>

DCBasePlugin::DCBasePlugin(const char* category, const char* funcname, const char* version)
{
    strcpy(m_category, category);
    strcpy(m_funcname, funcname);
    strcpy(m_version, version);
    m_timepos = NULL;
    m_pluginman = NULL;
}

DCBasePlugin::~DCBasePlugin()
{
    // do nothing
}

const char* DCBasePlugin::desc()
{
    return "function plugin";
}

int DCBasePlugin::initialize()
{
    return init();
}

int DCBasePlugin::call(void* input, void* output)
{
	DCPerfTimeCollect collect(m_timepos);
	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "@call start@: func[%s], version[%s], desc[%s]", \
			m_funcname, m_version, desc());

    int ret = process(input, output);

	DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "@call   end@: func[%s], version[%s]", \
			m_funcname, m_version);
	return ret;
}

int DCBasePlugin::call_all(void* input, void* output)
{
	std::vector<DCBasePlugin*>::iterator iter;
	int ret = 0;
	for(iter = m_calls.begin(); iter != m_calls.end(); iter++)
	{
		ret = (*iter)->call(input, output);
		if(ret)
		{
			return ret;
		}
	}
	return ret;
}

int DCBasePlugin::call_one(const char* func, void* input, void* output)
{
	std::vector<DCBasePlugin*>::iterator iter;
	int ret = UNSUPPORTEDFUNC;
	for(iter = m_calls.begin(); iter != m_calls.end(); iter++)
	{
        if(strcmp((*iter)->func(), func) == 0)
		{
            return (*iter)->call(input, output);
		}
	}
	DCSYSLOG(DCLOG_LEVEL_ERROR, UNSUPPORTEDFUNC, "not find func plugin: %s", func);
	return ret;
}

DCDBManer* DCBasePlugin::dbm()
{
	if(m_pluginman)
		return m_pluginman->get_dbm();
    return NULL;
}

DCAClient* DCBasePlugin::dca()
{
	if(m_pluginman)
		return (DCAClient*)m_pluginman->get_handle("DCA");
    return NULL;
}

DCRFData* DCBasePlugin::drf()
{
	if(m_pluginman)
		return m_pluginman->get_drf();
    return NULL;
}

void* DCBasePlugin::gethandle(const char* name)
{
	if(m_pluginman)
		return m_pluginman->get_handle(name);
    return NULL;
}

DCBasePlugin* DCBasePlugin::get_child_plugin(const char* func)
{
	std::vector<DCBasePlugin*>::iterator iter;
	for(iter = m_calls.begin(); iter != m_calls.end(); iter++)
	{
        if(strcmp((*iter)->func(), func) == 0)
		{
            return *iter;
		}
	}
	return NULL;
}
