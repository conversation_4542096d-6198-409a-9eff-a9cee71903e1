#include "DCRFData.h"
#include "DCDBManer.h"
#include "DCLogMacro.h"
#include <vector>
#include <string>
#include <map>
#include <list>
#include <pthread.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <unistd.h>
#include <stdlib.h>
/*************************************EMHead*******************************************/
// msgtype only support 32bit signed int
inline long entype(long pid, long type){return (type<<24)|(pid & 0x0FFFFFF);}
inline long detype(long mtype){return mtype>>24;}

#define RFSTIME 300  //服务响应间隔时间(暂设5分钟)

enum EMType
{
	EM_NONE 			= 0,	//only use to recive any msg
    EM_DumpData 		= 1,	//dump data request
    EM_RefreshData 		= 2,	//refresh data request
    EM_DumpTable		= 3,	//dump table data request
    EM_DumpSession		= 4,	//dump session info request
    EM_ImportTable		= 5,	//import table data request
};

struct EMHead
{
	long mtype;		// emtype(pid, type)
    long spid;		// 发送者的pid，用于返回应答消息
    long code;		// 附加的命令码/返回码
};

static inline int GetIPCKey()
{
	int key;
    const char* p = getenv("EMMSGKEY");
    if(p)
	{
        key = strtol(p, NULL, 16);
	}
	else
	{
		int uid = getuid();
		key = (0x0e11<<16)| uid&0xFFFF;
	}
    return key;
}

static int EMMsgGetId()
{
	static int key = GetIPCKey();
	int id = msgget(key,  IPC_CREAT|0660);
	if(id < 0)
	{
		return -1;
	}
	return id;
}

static int EMMsgSend(void * msgbuf, long msg_len, int msgflg)
{
    int id = EMMsgGetId();
    if(id < 0)
	{
		return -1;
	}
    return msgsnd(id, msgbuf, msg_len-sizeof(long), msgflg);
}

static int EMMsgRecv(void * msgbuf, long msg_len, long mtype, int msgflg)
{
	int id = EMMsgGetId();
    if(id < 0)
	{
		return -1;
	}
    return msgrcv(id, msgbuf, msg_len-sizeof(long), mtype, msgflg);
}

/*************************************STGuard*******************************************/
struct STGuard
{
public:
	STGuard(pthread_mutex_t* mutex)
		:m_smutex(mutex)
	{
		pthread_mutex_lock(m_smutex);
	}

	~STGuard()
	{
		pthread_mutex_unlock(m_smutex);
	}

private:
	pthread_mutex_t* m_smutex;
};

/*************************************DCRFDataImp****************************************/
class DCRFDataImp
{
public:
	/*构造函数*/
	DCRFDataImp()
		:m_nsec(1800)
		,m_run(true)
		,m_tid(0)
	{
		pthread_rwlock_init(&m_rwlock, NULL);
		pthread_mutex_init(&m_mutex, NULL);
        pthread_cond_init(&m_cond, NULL);
	}

	/*析构函数*/
	~DCRFDataImp()
	{
		m_run = false;
        pthread_join(m_tid, NULL);
		pthread_rwlock_destroy(&m_rwlock);
		pthread_mutex_destroy(&m_mutex);
		pthread_cond_destroy(&m_cond);
	}

	/*初始化, sqlfile 为sql文件名，nsec 为刷新间隔*/
	int init(const char* sqlfile, int nsec)
	{
		int ret = 0;
		STGuard guard(&m_mutex);

		if(nsec > 0) m_nsec = nsec;
		if(m_tid != (pthread_t)0)
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "already initialized");
			return 0;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to init DCDBManer for sql file [%s]", sqlfile);
		ret = m_dbm.Init(sqlfile, false);
        if(ret < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "init DCDBManer failed");
            return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to init DCDBManer");

        ret = pthread_create(&m_tid, NULL, work_routine, (void*)this);
        if(ret < 0)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "create work_routine failed:%s", strerror(errno));
			return ret;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to create work_routine");
		return 0;
	}

	/*注册刷新对象, nsec > 0 使用特定的刷新间隔*/
    int regist(const char* name, BData* base, int nsec = 0)
    {
    	int ret = 0;
    	DCRegNode nreg;
    	nreg.prf = NULL;
    	nreg.base = base;
		std::map<std::string, DCRegNode>::iterator it;

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to regist name[%s], base[%p], nsec[%d]", name, base, nsec);

		/*先进行查询注册，若重复则返回1*/
    	pthread_rwlock_wrlock(&m_rwlock);
		it = m_mreg.find(name);
		if(it != m_mreg.end())
		{
			ret = 1;
		}
		else
		{
			m_mreg[name] = nreg;
		}
		pthread_rwlock_unlock(&m_rwlock);

		if(ret == 1)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "repeated regist name[%s]: base[%p], nsec[%d]", name, base, nsec);
			return ret;
		}

		/*触发任务线程完成刷新注册，等待第一次刷新完成*/
		STTaskItem task;
		STRegTask rk(base, nsec);
        task.ntype = ETASK_REGIST;
		task.ptr = (void*)&rk;
		push_task(task);
		rk.wait();
		nreg.prf = rk.prf;

		/*注册失败，则删除查询map，注册成功，则更新数据*/
		pthread_rwlock_wrlock(&m_rwlock);
		if(!rk.prf)
		{
			m_mreg.erase(name);
			ret = -1;
		}
		else
		{
			m_mreg[name] = nreg;
			ret = 0;
		}
		pthread_rwlock_unlock(&m_rwlock);

        if(ret)
		{
			DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "failed to regist name[%s]: base[%p], nsec[%d]", name, base, nsec);
		}
		else
		{
			DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success to regist name[%s]", name);
		}
		return ret;
    }

	/*注销刷新对象*/
    void unregist(const char* name)
    {
    	DCRegNode nreg = {NULL};
		std::map<std::string, DCRegNode>::iterator it;

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "unregist name[%s]", name);

        /*先注销查询map数据*/
    	pthread_rwlock_wrlock(&m_rwlock);
		it = m_mreg.find(name);
		if(it != m_mreg.end())
		{
			nreg = it->second;
			m_mreg.erase(it);
		}
		pthread_rwlock_unlock(&m_rwlock);

		if(!nreg.prf)
		{
			return;
		}
		DCRegNode* preg = new DCRegNode();
		*preg = nreg;

		/*触发任务线程完成刷新注销*/
		STTaskItem task;
		task.ntype = ETASK_UNREGIST;
		task.ptr = (void*)preg;
		push_task(task);
    }

	/*按名获取数据*/
    BData* get(const char* name)
    {
    	BData* ptr = NULL;
    	std::map<std::string, DCRegNode>::iterator it;
    	pthread_rwlock_rdlock(&m_rwlock);
		it = m_mreg.find(name);
		if(it != m_mreg.end())
		{
			if(it->second.prf)
			{
				ptr = it->second.base;
			}
		}
		pthread_rwlock_unlock(&m_rwlock);
		return ptr;
    }

    /*触发手工刷新*/
	void refresh()
	{
		/*触发任务线程完成手工刷新*/
		DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "trigger to hand refresh");
		STTaskItem task;
		task.ntype = ETASK_REFRESH;
		task.ptr = NULL;
        push_task(task);
	}

	/*设置刷新间隔*/
	void setinval(int nsec)
	{
		m_nsec = nsec;
	}

	/*获取内部数据库管理句柄*/
	DCDBManer* dbm()
	{
        return &m_dbm;
	}

private:
	enum ETaskType
	{
        ETASK_REGIST = 1,			/*注册刷新任务*/
        ETASK_UNREGIST,				/*注销刷新任务*/
		ETASK_REFRESH				/*手工刷新任务*/
	};
    struct  DCRFNode
    {
        bool 	bflag;				/*false 默认刷新间隔， true 特定刷新间隔*/
		bool 	bSflag;				/*false 默认定时刷新， true 特定手动刷新*/
        int 	nsec;				/*刷新间隔*/
        time_t 	rf_time;			/*刷新截止时间*/
		time_t 	rf_Stime;			/*服务响应刷新截止时间*/
        EBDIDX 	used;				/*正在使用的数据索引*/
		std::vector<BData*> vdata;	/*刷新数据对象*/
    };

    struct DCRegNode
    {
		DCRFNode* prf;
		BData*    base;
    };

    struct STTaskItem
    {
        int ntype;
        void* ptr;
    };

    struct STRegTask
    {
    	DCRFNode*		prf;
        BData*			base;
        int				nsec;

        STRegTask(BData* b, int sec)
        	:prf(NULL)
        	,base(b)
        	,nsec(sec)
        	,bdone(false)
    	{
			pthread_mutex_init(&mutex, NULL);
        	pthread_cond_init(&cond, NULL);
    	}

    	~STRegTask()
    	{
    		pthread_cond_destroy(&cond);
			pthread_mutex_destroy(&mutex);
    	}

        void wait()
        {
			pthread_mutex_lock(&mutex);
			while(!bdone)
			{
                pthread_cond_wait(&cond, &mutex);
			}
			pthread_mutex_unlock(&mutex);
        }

        void done()
        {
			pthread_mutex_lock(&mutex);
			bdone = true;
			pthread_mutex_unlock(&mutex);
			pthread_cond_signal(&cond);
        }
	private:
		pthread_mutex_t	mutex;
        pthread_cond_t	cond;
        bool			bdone;
    };

private:
	static void* work_routine(void* handle)
	{
		DCRFDataImp* imp = static_cast<DCRFDataImp*>(handle);
		imp->svc();
		return NULL;
	}

	int svc()
	{
		int ret = 0;
		STTaskItem item;
		std::vector<DCRFNode*>::iterator it;
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "ready to handle refresh task");
		while(m_run)
		{
			ret = pop_task(item);
        	if(!ret)
			{
				do_task(item);
			}

			checkcmd();

			if(!m_lrf.empty())
			{
                m_dbm.CheckReset();
			}

        	for(it = m_lrf.begin(); it != m_lrf.end(); it++)
			{
				do_refresh(**it);
			}
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "end to handle refresh task");
		return 0;
	}

	int push_task(const STTaskItem& item)
	{
        pthread_mutex_lock(&m_mutex);
        m_ltask.push_back(item);
		pthread_mutex_unlock(&m_mutex);
        pthread_cond_signal(&m_cond);
        return 0;
	}

	int pop_task(STTaskItem& item)
	{
		pthread_mutex_lock(&m_mutex);
		if(m_ltask.empty())
		{
			struct timespec tp;
			tp.tv_sec = time(NULL) + 1;
            tp.tv_nsec = 0;
			pthread_cond_timedwait(&m_cond, &m_mutex, &tp);
			if(m_ltask.empty())
			{
				pthread_mutex_unlock(&m_mutex);
                return -1;
			}
		}
		item = m_ltask.front();
		m_ltask.pop_front();
		pthread_mutex_unlock(&m_mutex);
		return 0;
	}

	int do_task(const STTaskItem& item)
	{
		int ret = 0;
        switch(item.ntype)
        {
			case ETASK_REGIST :  ret = do_regist_task(item); break;
			case ETASK_UNREGIST: ret = do_unregist_task(item); break;
			case ETASK_REFRESH:  ret = do_refresh_task(item); break;
			default: break;
        }
        return ret;
	}

	int do_regist_task(const STTaskItem &item)
	{
		STRegTask &rk = *static_cast<STRegTask *>(item.ptr);
		bool bflag = rk.nsec ? true : false;

		if (int ret = rk.base->init(&m_dbm))
		{
			rk.prf = nullptr;
			rk.done();
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "BData.init failed for base[%p]", rk.base);
			return -1;
		}

		STGuard guard(&m_mutex);
		DCRFNode *found_rf = nullptr;
		EBDIDX uidx = EBDIDX0;
		for (auto it = m_lrf.begin(); it != m_lrf.end(); ++it)
		{
			DCRFNode *rf = *it;
			if (rf->bflag == bflag && bflag == false)
			{
				found_rf = rf;
				uidx = rf->used;
				break;
			}
			else if (rf->nsec == rk.nsec && rf->bflag == bflag && bflag == true)
			{
				found_rf = rf;
				uidx = rf->used;
				break;
			}
		}
		if (int ret = rk.base->work(uidx))
		{
			rk.prf = nullptr;
			rk.done();
			DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "BData.work failed for base[%p], used_idx[%d]", rk.base, uidx);
			return -1;
		}
		rk.base->set_used(uidx);

		if (found_rf)
		{
			found_rf->vdata.push_back(rk.base);
			rk.prf = found_rf;
		}
		else
		{
			DCRFNode *new_rf = new DCRFNode();
			if (!new_rf)
			{
				rk.prf = nullptr;
				rk.done();
				DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "Failed to allocate new DCRFNode");
				return -1;
			}
			new_rf->bflag = bflag;
			new_rf->nsec = bflag ? rk.nsec : m_nsec;
			new_rf->bSflag = (999999999 == rk.nsec) ? true : false;
			new_rf->rf_time = time(NULL) + new_rf->nsec;
			new_rf->rf_Stime = time(NULL) + RFSTIME;
			new_rf->used = uidx;
			new_rf->vdata.push_back(rk.base);
			m_lrf.push_back(new_rf);
			rk.prf = new_rf;
		}
		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "success do regist task for base[%p], used_idx[%d], nsec[%d], bSflag[%d]",
				 rk.base, uidx, rk.prf ? rk.prf->nsec : 0, rk.prf ? rk.prf->bSflag : false);

		rk.done();
		return 0;
	}

	int do_unregist_task(const STTaskItem& item)
	{
		DCRegNode* preg = static_cast<DCRegNode*>(item.ptr);
		std::vector<BData*>::iterator iv;

		DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "do unregist task for base[%p]", preg->base);

		for(iv = preg->prf->vdata.begin(); iv != preg->prf->vdata.end(); iv++)
		{
            if((*iv) == preg->base)
			{
				preg->prf->vdata.erase(iv);
				break;
			}
		}
		delete preg->base;

        if(preg->prf->vdata.empty())
		{
			std::vector<DCRFNode*>::iterator it;
            for(it = m_lrf.begin(); it != m_lrf.end(); it++)
			{
                if((*it) == preg->prf)
				{
					m_lrf.erase(it);
					break;
				}
			}
            delete preg->prf;
		}
		delete preg;
		return 0;
	}

	int do_refresh_task(const STTaskItem& item)
	{
	    DCSYSLOG(DCLOG_LEVEL_DVIEW, 0, "start to do hand refresh task");
		std::vector<DCRFNode*>::iterator it;
		time_t nw = time(NULL);
        for(it = m_lrf.begin(); it != m_lrf.end(); it++)
		{
		   if((*it)->rf_Stime <= nw && (*it)->bSflag == true) //达到服务响应时间间隔后且为手动刷新标识才能触发手动刷新
		   {	      
		      (*it)->rf_time = nw;
		   }
		   else if((*it)->bSflag == true && (*it)->rf_Stime > nw)
		   {
		      DCSYSLOG(DCLOG_LEVEL_ERROR, 0, "hand refresh task fail, with 5 minutes in between");
		   }
		}	
		return 0;
	}

	int do_refresh(DCRFNode& rf)
	{
		int ret = 0;
		EBDIDX idx = EBDIDX0;
        time_t nw = time(NULL);
        if(!rf.bflag && rf.nsec != m_nsec)
		{
			rf.nsec  = m_nsec;
		}
		if(rf.rf_time > nw)
		{
			return 0;
		}

		if(rf.used == EBDIDX0)
		{
			idx = EBDIDX1;
		}
		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "start to do refresh rf[%p], nsec[%d], used_idx[%d], flag[%d], Sflag[%d]", \
				&rf, rf.nsec, rf.used, rf.bflag, rf.bSflag);

		std::vector<BData*>::iterator iv;

		for( iv = rf.vdata.begin(); iv != rf.vdata.end(); iv++)
		{
			(*iv)->clear(idx);
            ret = (*iv)->work(idx);
            if(ret < 0)
			{
				rf.rf_time = time(NULL) + rf.nsec;
				rf.rf_Stime = time(NULL) + RFSTIME;

				DCSYSLOG(DCLOG_LEVEL_ERROR, 1, "failed to do refresh rf[%p], base[%p], now used_idx[%d]", \
				&rf, *iv, rf.used);
                return -1;
			}
		}

		for( iv = rf.vdata.begin(); iv != rf.vdata.end(); iv++)
		{
            (*iv)->set_used(idx);
		}
		rf.used = idx;
		rf.rf_time = time(NULL) + rf.nsec;
		rf.rf_Stime = time(NULL) + RFSTIME;

		DCSYSLOG(DCLOG_LEVEL_DEBUG, 0, "end to do refresh rf[%p], now used_idx[%d]", \
				&rf, rf.used);
        return 0;
	}

	void checkcmd()
	{
		EMHead emHead;
		long pid = getpid();
		// 手工刷新
		long type = entype(pid, EM_RefreshData);
		if(EMMsgRecv(&emHead, sizeof(emHead), type, IPC_NOWAIT) > 0)
		{
			STTaskItem item;
			do_refresh_task(item);
			emHead.mtype = entype(emHead.spid, EM_RefreshData);
			emHead.spid = pid;
			emHead.code = 0;
			EMMsgSend(&emHead, sizeof(emHead), 0);
		}
	}

private:
	int					m_nsec;
	bool				m_run;
	pthread_rwlock_t	m_rwlock;
	pthread_mutex_t 	m_mutex;
    pthread_cond_t		m_cond;
    pthread_t			m_tid;
	DCDBManer			m_dbm;

    std::vector<DCRFNode*>				m_lrf;
	std::map<std::string, DCRegNode> 	m_mreg;
	std::list<STTaskItem>				m_ltask;
};

/*************************************DCRFData*******************************************/

DCRFData* DCRFData::m_pinst = NULL;

DCRFData::DCRFData()
{
	m_imp = new DCRFDataImp();
}

DCRFData::~DCRFData()
{
	delete m_imp;
}

DCRFData* DCRFData::instance()
{
	if(!m_pinst)
	{
		m_pinst = new DCRFData();
	}
	return m_pinst;
}

int DCRFData::init(const char* sqlfile, int nsec)
{
	return m_imp->init(sqlfile, nsec);
}

int DCRFData::regist(const char* name, BData* base, int nsec)
{
	return m_imp->regist(name, base, nsec);
}

void DCRFData::unregist(const char* name)
{
	return m_imp->unregist(name);
}

BData* DCRFData::get(const char* name)
{
	return m_imp->get(name);
}

void DCRFData::refresh()
{
	m_imp->refresh();
}

void DCRFData::setinval(int nsec)
{
	m_imp->setinval(nsec);
}

DCDBManer* DCRFData::dbm()
{
	return m_imp->dbm();
}
